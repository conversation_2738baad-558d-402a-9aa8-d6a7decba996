/* 全局样式 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  
  --shadow-light: 0 4px 15px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
  
  --border-radius: 15px;
  --border-radius-lg: 20px;
  --border-radius-sm: 10px;
  
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
  box-sizing: border-box;
}

body {
  background: var(--primary-gradient);
  min-height: 100vh;
  font-family: 'Microsoft YaHei', 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 渐变背景类 */
.bg-gradient-primary {
  background: var(--primary-gradient) !important;
}

.bg-gradient-secondary {
  background: var(--secondary-gradient) !important;
}

.bg-gradient-success {
  background: var(--success-gradient) !important;
}

.bg-gradient-warning {
  background: var(--warning-gradient) !important;
}

.bg-gradient-danger {
  background: var(--danger-gradient) !important;
}

/* 加载屏幕 */
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: opacity 0.3s ease;
}

/* 主容器 */
.main-content {
  padding-top: 100px;
  padding-bottom: 50px;
}

/* 导航栏 */
.navbar {
  backdrop-filter: blur(15px);
  box-shadow: var(--shadow-light);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 年份控制区域 - 重新设计 */
.year-control-section {
  margin: 0 20px 30px;
}

.control-wrapper {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  min-height: 80px;
}

/* 年份导航区域 */
.year-section {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
  justify-content: flex-start;
}

.year-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  transition: var(--transition);
  cursor: pointer;
}

.year-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.year-btn:active {
  transform: scale(0.95);
}

.year-info {
  text-align: center;
  color: white;
}

.current-year {
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #fff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  display: block;
}

.chinese-year {
  font-size: 0.9rem;
  opacity: 0.8;
  font-weight: 500;
}

/* 农历信息区域 */
.lunar-section {
  text-align: center;
  color: white;
  flex: 1;
  padding: 0 20px;
}

.lunar-date {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
  background: linear-gradient(135deg, #ffd54f, #ffb74d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.solar-term {
  font-size: 0.85rem;
  opacity: 0.8;
  font-weight: 500;
}

/* 快捷操作区域 */
.actions-section {
  display: flex;
  gap: 12px;
  flex: 1;
  justify-content: flex-end;
}

.action-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  color: white;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: var(--transition);
  cursor: pointer;
  min-width: 100px;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:active {
  transform: translateY(0);
}

.action-btn-today {
  background: rgba(33, 150, 243, 0.3);
  border-color: rgba(33, 150, 243, 0.5);
}

.action-btn-today:hover {
  background: rgba(33, 150, 243, 0.5);
}

.action-btn-add {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.action-btn-add:hover {
  background: rgba(76, 175, 80, 0.5);
}





/* 日历网格区域 */
.calendar-section {
  margin: 0 20px 30px;
}

/* 月份卡片 */
.month-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius);
  padding: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--transition);
  cursor: pointer;
  height: 400px;
  min-height: 400px;
  overflow: visible;
  display: flex;
  flex-direction: column;
}

.month-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-heavy);
  border-color: rgba(102, 126, 234, 0.3);
}

.month-header {
  background: var(--primary-gradient);
  color: white;
  text-align: center;
  padding: 15px;
  border-radius: var(--border-radius-sm);
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 15px;
  box-shadow: var(--shadow-light);
}

/* 星期标题 */
.weekdays-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 3px;
  margin-bottom: 10px;
}

.weekday-cell {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  font-weight: 600;
  font-size: 0.85rem;
  padding: 8px 4px;
  text-align: center;
  border-radius: 6px;
  border: 1px solid rgba(102, 126, 234, 0.2);
}

/* 日期网格 */
.days-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 3px;
  flex: 1;
  min-height: 240px;
  grid-auto-rows: minmax(40px, auto);
}

.day-cell {
  padding: 6px 3px;
  font-size: 0.8rem;
  text-align: center;
  min-height: 40px;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  border-radius: 6px;
  transition: var(--transition);
  position: relative;
  cursor: pointer;
  border: 1px solid transparent;
  overflow: visible;
  word-wrap: break-word;
}

.day-cell:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(1.05);
  border-color: rgba(102, 126, 234, 0.3);
}

.day-cell.other-month {
  background: #f8f9fa;
  color: #adb5bd;
  opacity: 0.6;
}

.day-cell.today {
  background: var(--primary-gradient);
  color: white;
  font-weight: bold;
  box-shadow: var(--shadow-light);
}

.day-cell.weekend {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1976d2;
}

.day-cell.holiday-legal {
  background: var(--danger-gradient);
  color: #c62828;
  font-weight: bold;
}

.day-cell.holiday-traditional {
  background: var(--warning-gradient);
  color: #f57c00;
  font-weight: 600;
}

.day-cell.holiday-solar_term {
  background: linear-gradient(135deg, #e1bee7, #ce93d8);
  color: #7b1fa2;
  font-weight: 600;
}

.day-cell.has-memo {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
}

.day-number {
  font-size: 0.9rem;
  font-weight: 700;
  margin-bottom: 2px;
  line-height: 1.1;
  color: #333;
}

.lunar-date {
  font-size: 0.65rem;
  color: #7b68ee;
  margin-bottom: 1px;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-weight: 500;
}

.solar-term {
  font-size: 0.55rem;
  color: #ff6b6b;
  margin-bottom: 1px;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  font-weight: 600;
  background: linear-gradient(135deg, #ff9a9e, #fecfef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}



.memo-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #28a745;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.65rem;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.holiday-tag {
  font-size: 0.6rem;
  padding: 1px 4px;
  border-radius: 8px;
  margin-bottom: 2px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.2;
}

/* 统计信息区域 */
.stats-section {
  margin: 0 20px 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: var(--shadow-light);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--transition);
  display: flex;
  align-items: center;
  height: 100px;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  box-shadow: var(--shadow-light);
}

.stat-icon i {
  font-size: 1.5rem;
  color: white;
}

.stat-info h4 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 5px;
  color: #495057;
}

.stat-info p {
  margin: 0;
  color: #6c757d;
  font-weight: 500;
}

/* 天气预报窗口 */
.weather-widget {
  position: fixed;
  top: 100px;
  right: 20px;
  width: 380px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 1050;
  transition: var(--transition);
  transform: translateX(100%);
  opacity: 0;
  visibility: hidden;
}

.weather-widget.show {
  transform: translateX(0);
  opacity: 1;
  visibility: visible;
}

.weather-header {
  background: var(--primary-gradient);
  color: white;
  padding: 20px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
}

.weather-content {
  padding: 20px;
  max-height: 500px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding-top: 80px;
  }

  .year-control-section {
    margin: 0 10px 20px;
  }

  .control-wrapper {
    flex-direction: column;
    gap: 20px;
    padding: 20px 15px;
  }

  .year-section,
  .lunar-section,
  .actions-section {
    flex: none;
    width: 100%;
  }

  .year-section {
    justify-content: center;
  }

  .current-year {
    font-size: 1.8rem;
  }

  .actions-section {
    justify-content: center;
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    min-width: auto;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
  }

  .calendar-section {
    margin: 0 10px 20px;
  }

  .month-card {
    height: 320px;
    min-height: 320px;
    padding: 12px;
  }

  .stats-section {
    margin: 0 10px 20px;
  }

  .stat-card {
    margin-bottom: 15px;
    padding: 20px;
  }

  .weather-widget {
    width: calc(100vw - 20px);
    right: 10px;
    top: 80px;
  }
}

@media (max-width: 576px) {
  .day-cell {
    min-height: 35px;
    padding: 4px 2px;
    font-size: 0.75rem;
  }

  .day-number {
    font-size: 0.8rem;
    font-weight: 600;
  }

  .lunar-date {
    font-size: 0.6rem;
  }

  .memo-indicator {
    width: 16px;
    height: 16px;
    font-size: 0.6rem;
  }

  .month-card {
    height: 300px;
    min-height: 300px;
  }

  .days-grid {
    min-height: 210px;
    gap: 2px;
  }
}

/* 备忘录相关样式 */
.memo-list-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 10px;
  padding: 15px;
  background: #f8f9fa;
}

.memo-item {
  background: white;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #6c757d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: var(--transition);
  cursor: pointer;
}

.memo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.memo-item.priority-high {
  border-left-color: #dc3545;
}

.memo-item.priority-medium {
  border-left-color: #ffc107;
}

.memo-item.priority-low {
  border-left-color: #28a745;
}

.memo-item.status-completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.memo-item.status-completed .memo-title {
  text-decoration: line-through;
}

.memo-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.memo-title {
  font-weight: 600;
  font-size: 1.1rem;
  color: #495057;
  margin: 0;
  flex: 1;
}

.memo-meta {
  display: flex;
  gap: 10px;
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 8px;
}

.memo-content {
  color: #6c757d;
  margin-bottom: 10px;
  line-height: 1.5;
}

.memo-tags {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.memo-tag {
  background: #e9ecef;
  color: #495057;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.memo-actions {
  display: flex;
  gap: 5px;
  margin-top: 10px;
}

.memo-action-btn {
  background: none;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 4px 8px;
  font-size: 0.8rem;
  color: #6c757d;
  cursor: pointer;
  transition: var(--transition);
}

.memo-action-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.memo-empty {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.memo-empty i {
  font-size: 3rem;
  margin-bottom: 15px;
  opacity: 0.5;
}

/* 备忘录表单增强 */
.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.modal-header.bg-gradient-primary {
  background: var(--primary-gradient) !important;
  border-radius: 15px 15px 0 0;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 8px;
}

.form-control,
.form-select {
  border-radius: 8px;
  border: 1px solid #dee2e6;
  padding: 10px 12px;
  transition: var(--transition);
}

.form-control:focus,
.form-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 隐藏删除按钮的样式 */
.btn-hidden {
  display: none !important;
}

/* 打印样式 */
@media print {
  body {
    background: white !important;
  }

  .navbar,
  .weather-widget,
  .quick-actions,
  .stats-section {
    display: none !important;
  }

  .main-content {
    padding-top: 0;
  }

  .year-control-section,
  .month-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }

  .calendar-section .row {
    gap: 10px;
  }

  .month-card {
    break-inside: avoid;
    height: auto;
  }
}

/* 导航栏农历信息样式 */
.navbar-lunar-info {
  margin-left: auto;
  margin-right: 20px;
}

.lunar-info-container {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: var(--transition);
}

.lunar-info-container:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.lunar-date-nav {
  font-size: 0.9rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
  background: linear-gradient(135deg, #ffd54f, #ffb74d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.solar-term-nav {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 年份操作栏样式 */
.year-actions-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.year-navigation-compact {
  display: flex;
  align-items: center;
}

.year-display-compact {
  display: flex;
  align-items: center;
  margin: 0 10px;
}

.current-year-compact {
  font-size: 1.5rem;
  font-weight: 700;
  color: #495057;
}

.chinese-year-compact {
  font-size: 0.9rem;
  font-weight: 500;
}

.quick-actions-compact {
  display: flex;
  align-items: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-lunar-info {
    display: none !important;
  }

  .year-actions-bar .d-flex {
    flex-direction: column;
    gap: 15px;
  }

  .year-navigation-compact,
  .quick-actions-compact {
    justify-content: center;
  }

  .current-year-compact {
    font-size: 1.3rem;
  }
}

/* 报表样式 */
.report-summary .stat-card {
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.report-summary .stat-card:hover {
  transform: translateY(-5px);
}

.report-summary .stat-icon {
  font-size: 2.5rem;
  margin-right: 15px;
  opacity: 0.8;
}

.report-summary .stat-info h3 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.report-summary .stat-info p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

.progress-group {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
}

.progress-item {
  margin-bottom: 15px;
}

.progress-item:last-child {
  margin-bottom: 0;
}

.progress-item span {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.category-card {
  background: white;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.category-card:hover {
  transform: translateY(-2px);
}

.category-header {
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 10px;
}

.category-header h6 {
  margin: 0;
  font-weight: 600;
}

.category-stats small {
  color: #6c757d;
}

.priority-card {
  border: 2px solid;
  border-radius: 15px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.priority-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.report-content {
  min-height: 400px;
}

/* 打印样式 */
@media print {
  .modal-header,
  .modal-footer {
    display: none !important;
  }

  .modal-body {
    padding: 0 !important;
  }

  .report-content {
    page-break-inside: avoid;
  }

  .stat-card {
    break-inside: avoid;
    margin-bottom: 10px !important;
  }
}

/* 多选功能样式 */
.memo-item-wrapper {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 10px;
  transition: background-color 0.3s ease;
}

.memo-item-wrapper:hover {
  background-color: rgba(0, 123, 255, 0.05);
}

.memo-item-checkbox {
  margin-right: 12px;
  margin-top: 8px;
  flex-shrink: 0;
}

.memo-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.memo-item-wrapper .memo-item {
  flex: 1;
  margin: 0;
  cursor: pointer;
}

/* 批量操作栏样式 */
#batchOperationBar {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

#batchOperationBar .alert {
  border-left: 4px solid #17a2b8;
  background: linear-gradient(135deg, #e3f2fd, #f8f9fa);
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 全选复选框样式 */
#selectAllMemos {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

#selectAllMemos:indeterminate {
  accent-color: #ffc107;
}

/* 选中状态的备忘录项 */
.memo-item-wrapper:has(.memo-checkbox:checked) {
  background-color: rgba(40, 167, 69, 0.1);
  border: 2px solid rgba(40, 167, 69, 0.3);
}

.memo-item-wrapper:has(.memo-checkbox:checked) .memo-item {
  opacity: 0.8;
}

/* 批量操作按钮样式 */
#batchOperationBar .btn {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

#batchOperationBar .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 选中计数器样式 */
#selectedCount {
  font-weight: bold;
  color: #17a2b8;
  background: rgba(23, 162, 184, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  margin: 0 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .memo-item-wrapper {
    padding: 8px;
  }

  .memo-item-checkbox {
    margin-right: 8px;
    margin-top: 6px;
  }

  #batchOperationBar .alert {
    flex-direction: column;
    text-align: center;
  }

  #batchOperationBar .alert > div:last-child {
    margin-top: 10px;
  }

  #batchOperationBar .btn {
    margin: 2px;
    font-size: 0.85rem;
  }
}

/* 今天日期高亮样式 */
.today-highlight {
  background: linear-gradient(135deg, #ff6b6b, #ffa500) !important;
  color: white !important;
  border: 3px solid #ff4757 !important;
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.6) !important;
  transform: scale(1.1) !important;
  z-index: 10 !important;
  position: relative !important;
}

.today-highlight .day-number {
  color: white !important;
  font-weight: bold !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

.today-highlight .lunar-date {
  color: rgba(255, 255, 255, 0.9) !important;
}

.today-highlight .memo-count {
  background: rgba(255, 255, 255, 0.9) !important;
  color: #ff4757 !important;
  font-weight: bold !important;
}

/* 今天日期脉冲动画 */
@keyframes todayPulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.6);
    transform: scale(1.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.8);
    transform: scale(1.15);
  }
  100% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.6);
    transform: scale(1.1);
  }
}

/* 今天按钮增强样式 */
#todayBtn {
  position: relative;
  overflow: hidden;
}

#todayBtn:hover {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

#todayBtn:active {
  transform: translateY(0);
}

/* 今天按钮点击效果 */
#todayBtn.clicked {
  animation: buttonClick 0.3s ease-out;
}

@keyframes buttonClick {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}
