/**
 * 主应用程序
 */
class SmartCalendarApp {
  constructor() {
    this.isInitialized = false;
    this.init();
  }

  /**
   * 初始化应用
   */
  async init() {
    try {
      // 等待DOM加载完成
      if (document.readyState === 'loading') {
        await new Promise(resolve => {
          document.addEventListener('DOMContentLoaded', resolve);
        });
      }
      
      // 初始化认证管理器
      window.auth = new AuthManager();
      
      console.log('智能日历系统初始化完成');
      
    } catch (error) {
      console.error('应用初始化失败:', error);
      this.showErrorScreen(error);
    }
  }

  /**
   * 初始化应用主要功能（登录后调用）
   */
  initializeApp() {
    if (this.isInitialized) return;
    
    try {
      // 初始化各个模块
      window.calendar = new Calendar();
      window.memoManager = new MemoManager();
      window.weatherManager = new WeatherManager();
      
      // 绑定全局事件
      this.bindGlobalEvents();
      
      this.isInitialized = true;
      console.log('应用主要功能初始化完成');
      
    } catch (error) {
      console.error('应用功能初始化失败:', error);
    }
  }

  /**
   * 绑定全局事件
   */
  bindGlobalEvents() {
    // 窗口大小改变
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 页面可见性改变
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // 键盘快捷键
    document.addEventListener('keydown', this.handleKeydown.bind(this));
    
    // 页面卸载前保存数据
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    
    // 错误处理
    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));
  }

  /**
   * 处理窗口大小改变
   */
  handleResize() {
    // 调整天气窗口位置
    if (window.weatherManager && window.weatherManager.isVisible) {
      const widget = window.weatherManager.widget;
      const rect = widget.getBoundingClientRect();
      
      // 如果窗口超出屏幕，重新定位
      if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
        widget.style.right = '20px';
        widget.style.top = '100px';
        widget.style.left = 'auto';
        widget.style.transform = 'none';
      }
    }
  }

  /**
   * 处理页面可见性改变
   */
  handleVisibilityChange() {
    if (document.hidden) {
      // 页面隐藏时的处理
      console.log('页面隐藏');
    } else {
      // 页面显示时的处理
      console.log('页面显示');
    }
  }

  /**
   * 处理键盘快捷键
   */
  handleKeydown(e) {
    // Ctrl/Cmd + N: 新建备忘录
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
      e.preventDefault();
      if (window.memoManager) {
        window.memoManager.openModal();
      }
    }
    
    // Ctrl/Cmd + T: 跳转到今天
    if ((e.ctrlKey || e.metaKey) && e.key === 't') {
      e.preventDefault();
      if (window.calendar) {
        window.calendar.goToToday();
      }
    }
    
    // Ctrl/Cmd + W: 切换天气窗口
    if ((e.ctrlKey || e.metaKey) && e.key === 'w') {
      e.preventDefault();
      if (window.weatherManager) {
        window.weatherManager.toggle();
      }
    }
    
    // 左右箭头键切换年份
    if (e.key === 'ArrowLeft' && e.altKey) {
      e.preventDefault();
      if (window.calendar) {
        window.calendar.changeYear(-1);
      }
    }
    
    if (e.key === 'ArrowRight' && e.altKey) {
      e.preventDefault();
      if (window.calendar) {
        window.calendar.changeYear(1);
      }
    }
  }

  /**
   * 处理页面卸载前事件
   */
  handleBeforeUnload(e) {
    // 这里可以保存一些临时数据
    console.log('页面即将卸载');
  }

  /**
   * 处理JavaScript错误
   */
  handleError(e) {
    console.error('JavaScript错误:', e.error);
    this.showNotification('系统出现错误，请刷新页面重试', 'danger');
  }

  /**
   * 处理未捕获的Promise拒绝
   */
  handleUnhandledRejection(e) {
    console.error('未捕获的Promise拒绝:', e.reason);
    this.showNotification('系统出现异步错误', 'warning');
  }

  /**
   * 显示错误屏幕
   */
  showErrorScreen(error) {
    const errorHtml = `
      <div id="errorScreen" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" 
           style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); z-index: 9999;">
        <div class="text-center text-dark">
          <i class="bi bi-exclamation-triangle display-1 text-danger mb-4"></i>
          <h4 class="mb-3">系统初始化失败</h4>
          <p class="mb-4">${error.message || '未知错误'}</p>
          <button class="btn btn-primary" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise me-2"></i>重新加载
          </button>
        </div>
      </div>
    `;
    
    // 移除加载屏幕
    const loadingScreen = document.getElementById('loadingScreen');
    if (loadingScreen) {
      loadingScreen.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', errorHtml);
  }

  /**
   * 显示通知
   */
  showNotification(message, type = 'info', duration = 3000) {
    const notificationHtml = `
      <div class="toast align-items-center text-white bg-${type} border-0 position-fixed" 
           style="top: 100px; right: 20px; z-index: 9999;" role="alert">
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
      </div>
    `;
    
    const toastElement = document.createElement('div');
    toastElement.innerHTML = notificationHtml;
    document.body.appendChild(toastElement.firstElementChild);
    
    const toast = new bootstrap.Toast(toastElement.firstElementChild, {
      delay: duration
    });
    
    toast.show();
    
    // 自动移除
    setTimeout(() => {
      if (toastElement.firstElementChild.parentNode) {
        toastElement.firstElementChild.remove();
      }
    }, duration + 500);
  }

  /**
   * 获取应用状态
   */
  getAppState() {
    return {
      isInitialized: this.isInitialized,
      currentYear: window.calendar ? window.calendar.currentYear : null,
      isAuthenticated: window.auth ? window.auth.isAuthenticated() : false,
      currentUser: window.auth ? window.auth.getCurrentUser() : null
    };
  }

  /**
   * 重置应用
   */
  resetApp() {
    if (confirm('确定要重置所有数据吗？此操作不可撤销！')) {
      // 清除所有本地存储数据
      localStorage.clear();
      
      // 重新加载页面
      location.reload();
    }
  }

  /**
   * 销毁应用
   */
  destroy() {
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize);
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    document.removeEventListener('keydown', this.handleKeydown);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    window.removeEventListener('error', this.handleError);
    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
  }
}

// 启动应用
let app = null;

// 确保在DOM加载完成后启动应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    app = new SmartCalendarApp();
  });
} else {
  app = new SmartCalendarApp();
}

// 全局应用实例
window.app = app;
