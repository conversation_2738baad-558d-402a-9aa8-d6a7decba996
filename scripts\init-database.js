const mysql = require('mysql2/promise');
require('dotenv').config();

async function initDatabase() {
  let connection;
  
  try {
    console.log('🚀 开始初始化数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD
    });
    
    console.log('✅ 成功连接到MySQL服务器');
    
    // 创建数据库
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log(`✅ 数据库 ${process.env.DB_NAME} 创建成功`);
    
    // 选择数据库
    await connection.execute(`USE ${process.env.DB_NAME}`);
    
    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        avatar_url VARCHAR(255),
        timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
        language VARCHAR(10) DEFAULT 'zh-CN',
        theme VARCHAR(20) DEFAULT 'light',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login_at TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_username (username),
        INDEX idx_email (email),
        INDEX idx_created_at (created_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 用户表创建成功');
    
    // 创建备忘录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS memos (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        content TEXT,
        memo_date DATE NOT NULL,
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        category ENUM('work', 'personal', 'family', 'health', 'other') DEFAULT 'other',
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        reminder_time DATETIME NULL,
        is_all_day BOOLEAN DEFAULT TRUE,
        start_time TIME NULL,
        end_time TIME NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        tags JSON,
        attachments JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_date (user_id, memo_date),
        INDEX idx_priority (priority),
        INDEX idx_category (category),
        INDEX idx_status (status),
        INDEX idx_reminder (reminder_time),
        FULLTEXT idx_search (title, content)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 备忘录表创建成功');
    
    // 创建节假日表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS holidays (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        holiday_date DATE NOT NULL,
        type ENUM('legal', 'traditional', 'solar_term') NOT NULL,
        description TEXT,
        is_working_day BOOLEAN DEFAULT FALSE,
        year INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_date (holiday_date),
        INDEX idx_type (type),
        INDEX idx_year (year),
        UNIQUE KEY unique_holiday (holiday_date, name)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 节假日表创建成功');
    
    // 创建用户设置表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS user_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        setting_key VARCHAR(100) NOT NULL,
        setting_value JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_setting (user_id, setting_key),
        INDEX idx_user_key (user_id, setting_key)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 用户设置表创建成功');
    
    // 创建系统日志表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS system_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action VARCHAR(100) NOT NULL,
        resource_type VARCHAR(50),
        resource_id INT NULL,
        ip_address VARCHAR(45),
        user_agent TEXT,
        details JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_action (user_id, action),
        INDEX idx_created_at (created_at),
        INDEX idx_resource (resource_type, resource_id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);
    console.log('✅ 系统日志表创建成功');
    
    // 插入默认节假日数据
    await insertDefaultHolidays(connection);
    
    // 创建默认用户（可选）
    await createDefaultUser(connection);
    
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function insertDefaultHolidays(connection) {
  console.log('📅 插入默认节假日数据...');
  
  const holidays = [
    // 2025年法定节假日
    { name: '元旦', date: '2025-01-01', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-01-28', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-01-29', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-01-30', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-01-31', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-02-01', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-02-02', type: 'legal', year: 2025 },
    { name: '春节', date: '2025-02-03', type: 'legal', year: 2025 },
    { name: '清明节', date: '2025-04-04', type: 'legal', year: 2025 },
    { name: '清明节', date: '2025-04-05', type: 'legal', year: 2025 },
    { name: '清明节', date: '2025-04-06', type: 'legal', year: 2025 },
    { name: '劳动节', date: '2025-05-01', type: 'legal', year: 2025 },
    { name: '劳动节', date: '2025-05-02', type: 'legal', year: 2025 },
    { name: '劳动节', date: '2025-05-03', type: 'legal', year: 2025 },
    { name: '劳动节', date: '2025-05-04', type: 'legal', year: 2025 },
    { name: '劳动节', date: '2025-05-05', type: 'legal', year: 2025 },
    { name: '端午节', date: '2025-05-31', type: 'legal', year: 2025 },
    { name: '端午节', date: '2025-06-01', type: 'legal', year: 2025 },
    { name: '端午节', date: '2025-06-02', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-01', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-02', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-03', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-04', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-05', type: 'legal', year: 2025 },
    { name: '中秋节', date: '2025-10-06', type: 'legal', year: 2025 },
    { name: '国庆节', date: '2025-10-07', type: 'legal', year: 2025 },
    
    // 传统节日
    { name: '情人节', date: '2025-02-14', type: 'traditional', year: 2025 },
    { name: '妇女节', date: '2025-03-08', type: 'traditional', year: 2025 },
    { name: '青年节', date: '2025-05-04', type: 'traditional', year: 2025 },
    { name: '儿童节', date: '2025-06-01', type: 'traditional', year: 2025 },
    { name: '教师节', date: '2025-09-10', type: 'traditional', year: 2025 },
    { name: '平安夜', date: '2025-12-24', type: 'traditional', year: 2025 },
    { name: '圣诞节', date: '2025-12-25', type: 'traditional', year: 2025 },
    
    // 二十四节气
    { name: '立春', date: '2025-02-04', type: 'solar_term', year: 2025 },
    { name: '雨水', date: '2025-02-19', type: 'solar_term', year: 2025 },
    { name: '惊蛰', date: '2025-03-05', type: 'solar_term', year: 2025 },
    { name: '春分', date: '2025-03-20', type: 'solar_term', year: 2025 },
    { name: '清明', date: '2025-04-05', type: 'solar_term', year: 2025 },
    { name: '谷雨', date: '2025-04-20', type: 'solar_term', year: 2025 },
    { name: '立夏', date: '2025-05-05', type: 'solar_term', year: 2025 },
    { name: '小满', date: '2025-05-21', type: 'solar_term', year: 2025 },
    { name: '芒种', date: '2025-06-05', type: 'solar_term', year: 2025 },
    { name: '夏至', date: '2025-06-21', type: 'solar_term', year: 2025 },
    { name: '小暑', date: '2025-07-07', type: 'solar_term', year: 2025 },
    { name: '大暑', date: '2025-07-22', type: 'solar_term', year: 2025 },
    { name: '立秋', date: '2025-08-07', type: 'solar_term', year: 2025 },
    { name: '处暑', date: '2025-08-23', type: 'solar_term', year: 2025 },
    { name: '白露', date: '2025-09-07', type: 'solar_term', year: 2025 },
    { name: '秋分', date: '2025-09-23', type: 'solar_term', year: 2025 },
    { name: '寒露', date: '2025-10-08', type: 'solar_term', year: 2025 },
    { name: '霜降', date: '2025-10-23', type: 'solar_term', year: 2025 },
    { name: '立冬', date: '2025-11-07', type: 'solar_term', year: 2025 },
    { name: '小雪', date: '2025-11-22', type: 'solar_term', year: 2025 },
    { name: '大雪', date: '2025-12-07', type: 'solar_term', year: 2025 },
    { name: '冬至', date: '2025-12-22', type: 'solar_term', year: 2025 }
  ];
  
  for (const holiday of holidays) {
    try {
      await connection.execute(
        'INSERT IGNORE INTO holidays (name, holiday_date, type, year) VALUES (?, ?, ?, ?)',
        [holiday.name, holiday.date, holiday.type, holiday.year]
      );
    } catch (error) {
      console.warn(`插入节假日 ${holiday.name} 失败:`, error.message);
    }
  }
  
  console.log('✅ 默认节假日数据插入完成');
}

async function createDefaultUser(connection) {
  console.log('👤 创建默认用户...');
  
  const bcrypt = require('bcryptjs');
  const defaultPassword = await bcrypt.hash('123456', 10);
  
  try {
    await connection.execute(
      'INSERT IGNORE INTO users (username, email, password_hash, full_name) VALUES (?, ?, ?, ?)',
      ['admin', '<EMAIL>', defaultPassword, '系统管理员']
    );
    console.log('✅ 默认用户创建成功 (用户名: admin, 密码: 123456)');
  } catch (error) {
    console.warn('创建默认用户失败:', error.message);
  }
}

// 运行初始化
if (require.main === module) {
  initDatabase();
}

module.exports = { initDatabase };
