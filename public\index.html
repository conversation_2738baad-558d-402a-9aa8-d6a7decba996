<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能日历系统 - MySQL版</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Quill富文本编辑器 -->
  <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">

  <!-- 文件上传样式 -->
  <style>
    .file-upload-area {
      border: 2px dashed #dee2e6;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .file-upload-area:hover {
      border-color: #007bff;
      background-color: #f8f9fa;
    }

    .file-upload-area.dragover {
      border-color: #28a745;
      background-color: #d4edda;
    }

    .voice-recorder {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      background: #f8f9fa;
      border-radius: 8px;
      margin-top: 10px;
    }

    .voice-recorder.recording {
      background: #ffe6e6;
      border: 1px solid #dc3545;
    }

    .audio-visualizer {
      flex: 1;
      height: 30px;
      background: #e9ecef;
      border-radius: 15px;
      position: relative;
      overflow: hidden;
    }

    .audio-wave {
      height: 100%;
      background: linear-gradient(90deg, #007bff, #28a745);
      border-radius: 15px;
      width: 0%;
      transition: width 0.1s ease;
    }
  </style>
  <!-- 自定义样式 -->
  <link href="css/style.css" rel="stylesheet">
</head>
<body>
  <!-- 登录模态框（可选） -->
  <div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-person-circle me-2"></i>
            <span id="authModalTitle">用户登录</span>
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>

        <!-- 旧的表单已删除，使用新的模态框表单 -->
      </div>
    </div>
  </div>

  <!-- 主应用容器 -->
  <div id="app">
    <!-- 顶部导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand fw-bold" href="#">
          <i class="bi bi-calendar-event me-2"></i>
          智能日历系统
        </a>

      <!-- 农历信息 -->
      <div class="navbar-lunar-info d-none d-md-flex align-items-center">
        <div class="lunar-info-container">
          <div class="lunar-date-nav" id="currentLunarDate">农历腊月初七</div>
          <div class="solar-term-nav" id="currentSolarTerm">小寒后</div>
        </div>
      </div>
        
        <div class="navbar-nav ms-auto">
          <button class="btn btn-outline-light me-2" id="weatherBtn" title="天气预报">
            <i class="bi bi-cloud-sun"></i>
          </button>

          <button class="btn btn-outline-light me-2" id="memoListBtn" title="备忘录列表">
            <i class="bi bi-list-ul"></i>
          </button>

          <!-- 已登录用户菜单 -->
          <div class="nav-item dropdown" id="userMenu" style="display: none;">
            <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
              <i class="bi bi-person-circle me-1"></i>
              <span id="userDisplayName">用户</span>
            </a>
            <ul class="dropdown-menu">
              <li><h6 class="dropdown-header">
                <i class="bi bi-person-badge me-2"></i>
                <span id="userEmail"><EMAIL></span>
              </h6></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" id="profileBtn">
                <i class="bi bi-person me-2"></i>个人资料
              </a></li>
              <li><a class="dropdown-item" href="#" id="settingsBtn">
                <i class="bi bi-gear me-2"></i>用户设置
              </a></li>
              <li><a class="dropdown-item" href="#" id="changePasswordBtn">
                <i class="bi bi-key me-2"></i>修改密码
              </a></li>
              <li><a class="dropdown-item" href="#" id="dataManageBtn">
                <i class="bi bi-database me-2"></i>数据管理
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item text-danger" href="#" id="logoutBtn">
                <i class="bi bi-box-arrow-right me-2"></i>退出登录
              </a></li>
            </ul>
          </div>

          <!-- 未登录时的登录/注册按钮 -->
          <div id="authButtons">
            <a href="/login" class="btn btn-outline-light me-2" id="loginBtn">
              <i class="bi bi-box-arrow-in-right me-1"></i>登录
            </a>
            <a href="/register" class="btn btn-light" id="registerBtn">
              <i class="bi bi-person-plus me-1"></i>注册
            </a>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主内容区域 -->
    <main class="main-content">
      <!-- 年份和操作控制区域 -->
      <div class="year-actions-bar">
        <div class="container">
          <div class="d-flex justify-content-between align-items-center py-3">
            <!-- 年份导航 -->
            <div class="year-navigation-compact">
              <button type="button" class="btn btn-outline-primary btn-sm me-2" id="prevYear" title="上一年">
                <i class="bi bi-chevron-left"></i>
              </button>
              <span class="year-display-compact">
                <span class="current-year-compact" id="currentYear">2025</span>
                <small class="chinese-year-compact text-muted ms-2" id="chineseYear">乙巳蛇年</small>
              </span>
              <button type="button" class="btn btn-outline-primary btn-sm ms-2" id="nextYear" title="下一年">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions-compact">
              <button type="button" class="btn btn-outline-primary btn-sm me-2" id="todayBtn" onclick="handleTodayClick()">
                <i class="bi bi-calendar-check me-1"></i>今天
              </button>
              <button type="button" class="btn btn-success btn-sm" id="addMemoBtn" onclick="handleAddMemoClick()">
                <i class="bi bi-plus-circle me-1"></i>添加备忘录
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 日历网格区域 -->
      <div class="calendar-section">
        <div class="container">
          <!-- 3×4月份网格 -->
          <div class="row g-4" id="calendarGrid">
            <!-- 月份卡片将通过JavaScript动态生成 -->
          </div>
        </div>
      </div>

      <!-- 统计信息区域 -->
      <div class="stats-section">
        <div class="container">
          <div class="row">
            <div class="col-md-3">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="bi bi-calendar-event"></i>
                </div>
                <div class="stat-info">
                  <h4 id="totalMemos">0</h4>
                  <p>总备忘录</p>
                </div>
              </div>
            </div>
            
            <div class="col-md-3">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="bi bi-calendar-check"></i>
                </div>
                <div class="stat-info">
                  <h4 id="todayMemos">0</h4>
                  <p>今日备忘录</p>
                </div>
              </div>
            </div>
            
            <div class="col-md-3">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="bi bi-calendar-heart"></i>
                </div>
                <div class="stat-info">
                  <h4 id="holidays">12</h4>
                  <p>法定节假日</p>
                </div>
              </div>
            </div>
            
            <div class="col-md-3">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="bi bi-calendar-week"></i>
                </div>
                <div class="stat-info">
                  <h4 id="weekends">104</h4>
                  <p>周末天数</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>

  <!-- 加载状态 -->
  <div id="loadingScreen" class="loading-screen">
    <div class="text-center text-white">
      <div class="spinner-border mb-4" style="width: 3rem; height: 3rem;" role="status"></div>
      <h4 class="mb-2">智能日历系统</h4>
      <p class="mb-0">正在加载...</p>
    </div>
  </div>

  <!-- 天气预报浮动窗口 -->
  <div class="weather-widget" id="weatherWidget">
    <div class="weather-header">
      <h6 class="mb-0">
        <i class="bi bi-cloud-sun me-2"></i>天气预报
      </h6>
      <button type="button" class="btn-close btn-close-white" id="closeWeather"></button>
    </div>
    
    <div class="weather-content">
      <!-- 天气内容将通过JavaScript填充 -->
    </div>
  </div>

  <!-- 备忘录模态框 -->
  <div class="modal fade" id="memoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-gradient-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-journal-plus me-2"></i>
            <span id="memoModalTitle">添加备忘录</span>
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
          <form id="memoForm">
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="memoDate" class="form-label">
                  <i class="bi bi-calendar3 me-1"></i>日期
                </label>
                <input type="date" class="form-control" id="memoDate" required>
              </div>
              <div class="col-md-6">
                <label for="memoTime" class="form-label">
                  <i class="bi bi-clock me-1"></i>时间（可选）
                </label>
                <input type="time" class="form-control" id="memoTime">
              </div>
            </div>

            <div class="mb-3">
              <label for="memoTitle" class="form-label">
                <i class="bi bi-pencil me-1"></i>标题
              </label>
              <input type="text" class="form-control" id="memoTitle" placeholder="请输入备忘录标题" required>
            </div>

            <div class="mb-3">
              <label for="memoContent" class="form-label">
                <i class="bi bi-text-paragraph me-1"></i>内容
                <div class="btn-group btn-group-sm ms-2" role="group">
                  <button type="button" class="btn btn-outline-secondary" id="toggleRichText" title="切换富文本编辑">
                    <i class="bi bi-fonts"></i>
                  </button>
                  <button type="button" class="btn btn-outline-secondary" id="addImageBtn" title="添加图片">
                    <i class="bi bi-image"></i>
                  </button>
                  <button type="button" class="btn btn-outline-secondary" id="voiceRecordBtn" title="语音录制">
                    <i class="bi bi-mic"></i>
                  </button>
                </div>
              </label>

              <!-- 普通文本编辑器 -->
              <textarea class="form-control" id="memoContent" rows="4" placeholder="请输入备忘录内容（可选）"></textarea>

              <!-- 富文本编辑器容器 -->
              <div id="richTextEditor" style="display: none;">
                <div id="quillEditor" style="height: 200px;"></div>
              </div>

              <!-- 图片上传区域 -->
              <div class="file-upload-area mt-2" id="imageUploadArea" style="display: none;">
                <i class="bi bi-cloud-upload fs-1 text-muted"></i>
                <p class="mb-1">拖拽图片到此处或点击上传</p>
                <small class="text-muted">支持 JPG, PNG, GIF 格式，最大 5MB</small>
                <input type="file" id="imageInput" accept="image/*" multiple style="display: none;">
              </div>

              <!-- 已上传图片预览 -->
              <div id="imagePreview" class="mt-2"></div>

              <!-- 语音录制区域 -->
              <div class="voice-recorder" id="voiceRecorder" style="display: none;">
                <button type="button" class="btn btn-danger btn-sm" id="recordButton">
                  <i class="bi bi-record-circle"></i> 开始录制
                </button>
                <div class="audio-visualizer">
                  <div class="audio-wave" id="audioWave"></div>
                </div>
                <span id="recordTime">00:00</span>
                <button type="button" class="btn btn-success btn-sm" id="stopRecord" style="display: none;">
                  <i class="bi bi-stop-circle"></i> 停止
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" id="playRecord" style="display: none;">
                  <i class="bi bi-play-circle"></i> 播放
                </button>
              </div>

              <!-- 录音文件预览 -->
              <div id="audioPreview" class="mt-2"></div>
            </div>

            <div class="row mb-3">
              <div class="col-md-4">
                <label for="memoPriority" class="form-label">
                  <i class="bi bi-flag me-1"></i>优先级
                </label>
                <select class="form-select" id="memoPriority">
                  <option value="low">🟢 低</option>
                  <option value="medium" selected>🟡 中</option>
                  <option value="high">🔴 高</option>
                </select>
              </div>

              <div class="col-md-4">
                <label for="memoCategory" class="form-label">
                  <i class="bi bi-tags me-1"></i>分类
                </label>
                <select class="form-select" id="memoCategory">
                  <option value="work">💼 工作</option>
                  <option value="personal">👤 个人</option>
                  <option value="family">👨‍👩‍👧‍👦 家庭</option>
                  <option value="health">🏥 健康</option>
                  <option value="study">📚 学习</option>
                  <option value="travel">✈️ 旅行</option>
                  <option value="other">📝 其他</option>
                </select>
              </div>

              <div class="col-md-4">
                <label for="memoStatus" class="form-label">
                  <i class="bi bi-check-circle me-1"></i>状态
                </label>
                <select class="form-select" id="memoStatus">
                  <option value="pending">⏳ 待办</option>
                  <option value="in_progress">🔄 进行中</option>
                  <option value="completed">✅ 已完成</option>
                  <option value="cancelled">❌ 已取消</option>
                </select>
              </div>
            </div>

            <div class="mb-3">
              <label for="memoReminder" class="form-label">
                <i class="bi bi-bell me-1"></i>提醒设置
              </label>
              <select class="form-select" id="memoReminder">
                <option value="">无提醒</option>
                <option value="5">提前5分钟</option>
                <option value="15">提前15分钟</option>
                <option value="30">提前30分钟</option>
                <option value="60">提前1小时</option>
                <option value="1440">提前1天</option>
              </select>
            </div>

            <div class="mb-3">
              <label for="memoRecurring" class="form-label">
                <i class="bi bi-arrow-repeat me-1"></i>重复设置
              </label>
              <select class="form-select" id="memoRecurring">
                <option value="">不重复</option>
                <option value="daily">每天</option>
                <option value="weekly">每周</option>
                <option value="monthly">每月</option>
                <option value="yearly">每年</option>
                <option value="custom">自定义</option>
              </select>
            </div>

            <!-- 重复设置详细选项 -->
            <div class="mb-3" id="recurringOptions" style="display: none;">
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0"><i class="bi bi-gear me-1"></i>重复详细设置</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <label for="recurringInterval" class="form-label">间隔</label>
                      <div class="input-group">
                        <input type="number" class="form-control" id="recurringInterval" value="1" min="1">
                        <select class="form-select" id="recurringUnit">
                          <option value="days">天</option>
                          <option value="weeks">周</option>
                          <option value="months">月</option>
                          <option value="years">年</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-6">
                      <label for="recurringEndDate" class="form-label">结束日期</label>
                      <input type="date" class="form-control" id="recurringEndDate">
                    </div>
                  </div>

                  <!-- 星期选择（仅周重复时显示） -->
                  <div class="mt-3" id="weeklyOptions" style="display: none;">
                    <label class="form-label">重复星期</label>
                    <div class="btn-group" role="group">
                      <input type="checkbox" class="btn-check" id="sun" value="0">
                      <label class="btn btn-outline-primary btn-sm" for="sun">日</label>

                      <input type="checkbox" class="btn-check" id="mon" value="1">
                      <label class="btn btn-outline-primary btn-sm" for="mon">一</label>

                      <input type="checkbox" class="btn-check" id="tue" value="2">
                      <label class="btn btn-outline-primary btn-sm" for="tue">二</label>

                      <input type="checkbox" class="btn-check" id="wed" value="3">
                      <label class="btn btn-outline-primary btn-sm" for="wed">三</label>

                      <input type="checkbox" class="btn-check" id="thu" value="4">
                      <label class="btn btn-outline-primary btn-sm" for="thu">四</label>

                      <input type="checkbox" class="btn-check" id="fri" value="5">
                      <label class="btn btn-outline-primary btn-sm" for="fri">五</label>

                      <input type="checkbox" class="btn-check" id="sat" value="6">
                      <label class="btn btn-outline-primary btn-sm" for="sat">六</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="memoTags" class="form-label">
                <i class="bi bi-hash me-1"></i>标签（用逗号分隔）
              </label>
              <input type="text" class="form-control" id="memoTags" placeholder="例如：重要,紧急,会议">
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>取消
          </button>
          <button type="button" class="btn btn-outline-danger" id="deleteMemo" style="display: none;">
            <i class="bi bi-trash me-1"></i>删除
          </button>
          <button type="button" class="btn btn-success" id="saveMemo">
            <i class="bi bi-check-circle me-1"></i>保存
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 备忘录列表模态框 -->
  <div class="modal fade" id="memoListModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header bg-gradient-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-list-ul me-2"></i>
            <span id="memoListTitle">备忘录列表</span>
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
          <!-- 批量操作栏 -->
          <div class="row mb-3" id="batchOperationBar" style="display: none;">
            <div class="col-12">
              <div class="alert alert-info d-flex justify-content-between align-items-center">
                <div>
                  <i class="bi bi-check-square me-2"></i>
                  已选择 <span id="selectedCount">0</span> 个备忘录
                </div>
                <div>
                  <button type="button" class="btn btn-sm btn-outline-danger me-2" id="batchDeleteSelected">
                    <i class="bi bi-trash me-1"></i>删除选中
                  </button>
                  <button type="button" class="btn btn-sm btn-outline-success me-2" id="batchCompleteSelected">
                    <i class="bi bi-check-circle me-1"></i>标记完成
                  </button>
                  <button type="button" class="btn btn-sm btn-outline-secondary" id="cancelSelection">
                    <i class="bi bi-x me-1"></i>取消选择
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 搜索和筛选 -->
          <div class="row mb-3">
            <div class="col-md-3">
              <div class="input-group">
                <span class="input-group-text">
                  <input type="checkbox" id="selectAllMemos" title="全选/取消全选">
                </span>
                <input type="text" class="form-control" id="memoSearch" placeholder="搜索备忘录...">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select" id="memoFilterCategory">
                <option value="">所有分类</option>
                <option value="work">💼 工作</option>
                <option value="personal">👤 个人</option>
                <option value="family">👨‍👩‍👧‍👦 家庭</option>
                <option value="health">🏥 健康</option>
                <option value="study">📚 学习</option>
                <option value="travel">✈️ 旅行</option>
                <option value="other">📝 其他</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-select" id="memoFilterStatus">
                <option value="">所有状态</option>
                <option value="pending">⏳ 待办</option>
                <option value="in_progress">🔄 进行中</option>
                <option value="completed">✅ 已完成</option>
                <option value="cancelled">❌ 已取消</option>
              </select>
            </div>
            <div class="col-md-2">
              <div class="btn-group w-100" role="group">
                <button type="button" class="btn btn-primary" id="addNewMemo">
                  <i class="bi bi-plus"></i> 新建
                </button>
                <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                  <i class="bi bi-file-earmark-bar-graph"></i>
                </button>
                <ul class="dropdown-menu">
                  <li><a class="dropdown-item" href="#" id="generateReport">
                    <i class="bi bi-file-earmark-text me-2"></i>生成报表
                  </a></li>
                  <li><a class="dropdown-item" href="#" id="exportMemos">
                    <i class="bi bi-download me-2"></i>导出数据
                  </a></li>
                  <li><hr class="dropdown-divider"></li>
                  <li><a class="dropdown-item" href="#" id="batchDelete">
                    <i class="bi bi-trash me-2"></i>批量删除
                  </a></li>
                </ul>
              </div>
            </div>
          </div>

          <!-- 备忘录列表 -->
          <div id="memoListContainer" class="memo-list-container">
            <!-- 备忘录项目将通过JavaScript动态生成 -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 登录模态框 -->
  <div class="modal fade" id="loginModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-box-arrow-in-right me-2"></i>用户登录
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="loginForm">
            <div class="mb-3">
              <label for="loginUsername" class="form-label">
                <i class="bi bi-person me-1"></i>用户名或邮箱 <span class="text-danger">*</span>
              </label>
              <input type="text" class="form-control" id="loginUsername" required
                     placeholder="请输入用户名或邮箱">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>支持用户名或邮箱登录
              </div>
            </div>
            <div class="mb-3">
              <label for="loginPassword" class="form-label">
                <i class="bi bi-lock me-1"></i>密码 <span class="text-danger">*</span>
              </label>
              <input type="password" class="form-control" id="loginPassword" required
                     placeholder="请输入密码">
            </div>
            <div class="mb-3 form-check">
              <input type="checkbox" class="form-check-input" id="rememberMe">
              <label class="form-check-label" for="rememberMe">
                <i class="bi bi-clock-history me-1"></i>记住我（延长登录有效期）
              </label>
            </div>

            <!-- 必填字段说明 -->
            <div class="alert alert-info py-2 mb-3">
              <small>
                <i class="bi bi-exclamation-circle me-1"></i>
                标有 <span class="text-danger">*</span> 的字段为必填项
              </small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="loginSubmitBtn">
            <i class="bi bi-box-arrow-in-right me-1"></i>登录
          </button>
        </div>
        <div class="text-center pb-3">
          <small class="text-muted">
            没有账户？<a href="#" id="showRegisterFromLogin">立即注册</a>
          </small>
        </div>
      </div>
    </div>
  </div>

  <!-- 注册模态框 -->
  <div class="modal fade" id="registerModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-success text-white">
          <h5 class="modal-title">
            <i class="bi bi-person-plus me-2"></i>用户注册
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="registerForm">
            <div class="mb-3">
              <label for="registerUsername" class="form-label">
                <i class="bi bi-person me-1"></i>用户名 <span class="text-danger">*</span>
              </label>
              <input type="text" class="form-control" id="registerUsername" required
                     placeholder="请输入用户名" maxlength="20" minlength="3">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>3-20个字符，只能包含字母、数字和下划线
              </div>
            </div>
            <div class="mb-3">
              <label for="registerEmail" class="form-label">
                <i class="bi bi-envelope me-1"></i>邮箱 <span class="text-danger">*</span>
              </label>
              <input type="email" class="form-control" id="registerEmail" required
                     placeholder="请输入邮箱地址">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>用于登录和找回密码
              </div>
            </div>
            <div class="mb-3">
              <label for="registerFullName" class="form-label">
                <i class="bi bi-person-badge me-1"></i>姓名 <span class="text-danger">*</span>
              </label>
              <input type="text" class="form-control" id="registerFullName" required
                     placeholder="请输入真实姓名或昵称">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>用于显示和个人资料
              </div>
            </div>
            <div class="mb-3">
              <label for="registerPassword" class="form-label">
                <i class="bi bi-lock me-1"></i>密码 <span class="text-danger">*</span>
              </label>
              <input type="password" class="form-control" id="registerPassword" required
                     placeholder="请输入密码" minlength="6">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>至少6个字符，建议包含字母和数字
              </div>
            </div>
            <div class="mb-3">
              <label for="confirmPassword" class="form-label">
                <i class="bi bi-lock-fill me-1"></i>确认密码 <span class="text-danger">*</span>
              </label>
              <input type="password" class="form-control" id="confirmPassword" required
                     placeholder="请再次输入密码">
              <div class="form-text">
                <i class="bi bi-info-circle me-1"></i>请确保两次输入的密码一致
              </div>
            </div>

            <!-- 必填字段说明 -->
            <div class="alert alert-info py-2 mb-3">
              <small>
                <i class="bi bi-exclamation-circle me-1"></i>
                标有 <span class="text-danger">*</span> 的字段为必填项
              </small>
            </div>

            <!-- 调试按钮 -->
            <div class="alert alert-warning py-2 mb-3">
              <small>
                <strong>调试模式：</strong>
                <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="window.debugRegisterForm()">
                  <i class="bi bi-bug"></i> 调试表单
                </button>
                <button type="button" class="btn btn-sm btn-outline-info ms-1" onclick="window.fillTestData()">
                  <i class="bi bi-clipboard-data"></i> 填充测试数据
                </button>
              </small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-success" id="registerSubmitBtn">
            <i class="bi bi-person-plus me-1"></i>注册
          </button>
        </div>
        <div class="text-center pb-3">
          <small class="text-muted">
            已有账户？<a href="#" id="showLoginFromRegister">立即登录</a>
          </small>
        </div>
      </div>
    </div>
  </div>

  <!-- 个人资料模态框 -->
  <div class="modal fade" id="profileModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-info text-white">
          <h5 class="modal-title">
            <i class="bi bi-person me-2"></i>个人资料
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="profileForm">
            <div class="mb-3">
              <label for="profileUsername" class="form-label">
                <i class="bi bi-person me-1"></i>用户名
              </label>
              <input type="text" class="form-control" id="profileUsername" readonly>
            </div>
            <div class="mb-3">
              <label for="profileEmail" class="form-label">
                <i class="bi bi-envelope me-1"></i>邮箱
              </label>
              <input type="email" class="form-control" id="profileEmail">
            </div>
            <div class="mb-3">
              <label for="profileFullName" class="form-label">
                <i class="bi bi-person-badge me-1"></i>姓名
              </label>
              <input type="text" class="form-control" id="profileFullName">
            </div>
            <div class="mb-3">
              <label class="form-label">
                <i class="bi bi-calendar me-1"></i>注册时间
              </label>
              <input type="text" class="form-control" id="profileCreatedAt" readonly>
            </div>
            <div class="mb-3">
              <label class="form-label">
                <i class="bi bi-activity me-1"></i>最后登录
              </label>
              <input type="text" class="form-control" id="profileLastLogin" readonly>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-info" id="profileUpdateBtn">
            <i class="bi bi-check-circle me-1"></i>更新资料
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 修改密码模态框 -->
  <div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-warning text-dark">
          <h5 class="modal-title">
            <i class="bi bi-key me-2"></i>修改密码
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <form id="changePasswordForm">
            <div class="mb-3">
              <label for="currentPassword" class="form-label">
                <i class="bi bi-lock me-1"></i>当前密码
              </label>
              <input type="password" class="form-control" id="currentPassword" required>
            </div>
            <div class="mb-3">
              <label for="newPassword" class="form-label">
                <i class="bi bi-lock-fill me-1"></i>新密码
              </label>
              <input type="password" class="form-control" id="newPassword" required>
              <div class="form-text">至少6个字符</div>
            </div>
            <div class="mb-3">
              <label for="confirmNewPassword" class="form-label">
                <i class="bi bi-shield-lock me-1"></i>确认新密码
              </label>
              <input type="password" class="form-control" id="confirmNewPassword" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-warning" id="changePasswordSubmitBtn">
            <i class="bi bi-key me-1"></i>修改密码
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 报表模态框 -->
  <div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header bg-gradient-success text-white">
          <h5 class="modal-title">
            <i class="bi bi-file-earmark-bar-graph me-2"></i>
            备忘录统计报表
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
          <!-- 报表控制面板 -->
          <div class="row mb-4">
            <div class="col-md-3">
              <label class="form-label">报表类型</label>
              <select class="form-select" id="reportType">
                <option value="summary">总体统计</option>
                <option value="category">分类统计</option>
                <option value="priority">优先级统计</option>
                <option value="status">状态统计</option>
                <option value="monthly">月度统计</option>
              </select>
            </div>
            <div class="col-md-3">
              <label class="form-label">时间范围</label>
              <select class="form-select" id="reportTimeRange">
                <option value="all">全部时间</option>
                <option value="thisMonth">本月</option>
                <option value="lastMonth">上月</option>
                <option value="thisYear">今年</option>
                <option value="custom">自定义</option>
              </select>
            </div>
            <div class="col-md-3" id="customDateRange" style="display: none;">
              <label class="form-label">开始日期</label>
              <input type="date" class="form-control" id="reportStartDate">
            </div>
            <div class="col-md-3" id="customDateRange2" style="display: none;">
              <label class="form-label">结束日期</label>
              <input type="date" class="form-control" id="reportEndDate">
            </div>
            <div class="col-md-3 d-flex align-items-end">
              <button type="button" class="btn btn-primary w-100" id="generateReportBtn">
                <i class="bi bi-bar-chart"></i> 生成报表
              </button>
            </div>
          </div>

          <!-- 报表内容 -->
          <div id="reportContent">
            <div class="text-center text-muted py-5">
              <i class="bi bi-file-earmark-bar-graph" style="font-size: 3rem;"></i>
              <p class="mt-3">请选择报表类型并点击"生成报表"</p>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
            <i class="bi bi-x-circle me-1"></i>关闭
          </button>
          <button type="button" class="btn btn-success" id="exportReportBtn">
            <i class="bi bi-download me-1"></i>导出报表
          </button>
          <button type="button" class="btn btn-primary" id="printReportBtn">
            <i class="bi bi-printer me-1"></i>打印报表
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Quill富文本编辑器 -->
  <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

  <!-- 农历计算库 -->
  <script src="js/lunar-calendar.js"></script>

  <!-- 增强的日历脚本 -->
  <script>
    // 全局函数：处理添加备忘录按钮点击
    function handleAddMemoClick() {
      console.log('🎯 添加备忘录按钮被点击（onclick方式）');
      if (window.memoManager) {
        window.memoManager.openMemoModal();
        console.log('✅ 调用 memoManager.openMemoModal()');
      } else {
        console.error('❌ window.memoManager 未找到');
        alert('备忘录管理器未初始化，请刷新页面重试');
      }
    }

    // 全局函数：处理今天按钮点击
    function handleTodayClick() {
      console.log('🎯 今天按钮被点击');

      // 添加按钮点击效果
      const todayBtn = document.getElementById('todayBtn');
      if (todayBtn) {
        todayBtn.classList.add('clicked');
        setTimeout(() => {
          todayBtn.classList.remove('clicked');
        }, 300);
      }

      const today = new Date();
      const year = today.getFullYear();
      const month = today.getMonth() + 1;
      const day = today.getDate();

      console.log(`📅 跳转到今天: ${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`);

      // 高亮今天的日期
      highlightTodayDate(year, month, day);

      // 滚动到今天所在的月份
      scrollToMonth(month);

      // 显示成功提示
      if (window.memoManager) {
        window.memoManager.showNotification(`📅 已跳转到今天 ${month}月${day}日`, 'success');
      }
    }

    // 高亮今天日期的函数
    function highlightTodayDate(year, month, day) {
      // 移除之前的高亮
      document.querySelectorAll('.today-highlight').forEach(el => {
        el.classList.remove('today-highlight');
      });

      // 查找今天的日期元素
      const monthCards = document.querySelectorAll('.month-card');
      monthCards.forEach((card, index) => {
        if (index + 1 === month) {
          const dayElements = card.querySelectorAll('.calendar-day');
          dayElements.forEach(dayEl => {
            const dayNumber = parseInt(dayEl.querySelector('.day-number')?.textContent);
            if (dayNumber === day) {
              dayEl.classList.add('today-highlight');
              console.log(`✅ 已高亮今天日期: ${month}月${day}日`);

              // 添加闪烁效果
              dayEl.style.animation = 'todayPulse 2s ease-in-out 3';
            }
          });
        }
      });
    }

    // 滚动到指定月份
    function scrollToMonth(month) {
      const monthCards = document.querySelectorAll('.month-card');
      if (monthCards[month - 1]) {
        monthCards[month - 1].scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        console.log(`📍 已滚动到 ${month} 月`);
      }
    }

    // 用户管理类
    class UserManager {
      constructor() {
        this.currentUser = null;
        this.token = localStorage.getItem('authToken');
        this.init();
      }

      init() {
        this.bindEvents();
        this.checkAuthStatus();
      }

      bindEvents() {
        // 登录/注册按钮现在是链接，无需JavaScript事件处理
        // 如果需要额外的处理逻辑，可以在这里添加

        // 模态框切换事件
        document.getElementById('showRegisterFromLogin').addEventListener('click', (e) => {
          e.preventDefault();
          this.switchToRegister();
        });

        document.getElementById('showLoginFromRegister').addEventListener('click', (e) => {
          e.preventDefault();
          this.switchToLogin();
        });

        // 表单提交事件
        document.getElementById('loginSubmitBtn').addEventListener('click', () => {
          this.handleLogin();
        });

        document.getElementById('registerSubmitBtn').addEventListener('click', () => {
          this.handleRegister();
        });

        // 用户菜单事件
        document.getElementById('profileBtn').addEventListener('click', (e) => {
          e.preventDefault();
          this.showProfileModal();
        });

        document.getElementById('changePasswordBtn').addEventListener('click', (e) => {
          e.preventDefault();
          this.showChangePasswordModal();
        });

        document.getElementById('logoutBtn').addEventListener('click', (e) => {
          e.preventDefault();
          this.handleLogout();
        });

        // 个人资料更新
        document.getElementById('profileUpdateBtn').addEventListener('click', () => {
          this.updateProfile();
        });

        // 修改密码
        document.getElementById('changePasswordSubmitBtn').addEventListener('click', () => {
          this.changePassword();
        });
      }

      async checkAuthStatus() {
        console.log('🔍 检查认证状态...', { token: !!this.token });

        if (this.token) {
          try {
            console.log('🔑 发送认证请求...');
            const response = await fetch('/api/auth/me', {
              headers: {
                'Authorization': `Bearer ${this.token}`
              }
            });

            if (response.ok) {
              const data = await response.json();
              console.log('✅ 用户已登录:', data.user);
              this.setCurrentUser(data.user);
            } else {
              console.log('❌ Token无效，清除认证状态');
              this.clearAuth();
            }
          } catch (error) {
            console.error('检查认证状态失败:', error);
            this.clearAuth();
          }
        } else {
          console.log('🔓 无Token，显示登录按钮');
          this.showAuthButtons();
        }
      }

      setCurrentUser(user) {
        this.currentUser = user;
        this.updateUserDisplay();
        this.showUserMenu();
      }

      updateUserDisplay() {
        if (this.currentUser) {
          document.getElementById('userDisplayName').textContent = this.currentUser.full_name || this.currentUser.username;
          document.getElementById('userEmail').textContent = this.currentUser.email;
        }
      }

      showUserMenu() {
        document.getElementById('userMenu').style.display = 'block';
        document.getElementById('authButtons').style.display = 'none';
      }

      showAuthButtons() {
        console.log('🔍 显示登录按钮...');
        const userMenu = document.getElementById('userMenu');
        const authButtons = document.getElementById('authButtons');

        if (userMenu) {
          userMenu.style.display = 'none';
          console.log('✅ 隐藏用户菜单');
        } else {
          console.error('❌ 找不到userMenu元素');
        }

        if (authButtons) {
          authButtons.style.display = 'block';
          console.log('✅ 显示登录/注册按钮');
        } else {
          console.error('❌ 找不到authButtons元素');
        }
      }

      clearAuth() {
        this.currentUser = null;
        this.token = null;
        localStorage.removeItem('authToken');
        this.showAuthButtons();
      }

      showLoginModal() {
        const modal = new bootstrap.Modal(document.getElementById('loginModal'));
        modal.show();
      }

      showRegisterModal() {
        const modal = new bootstrap.Modal(document.getElementById('registerModal'));
        modal.show();
      }

      switchToRegister() {
        bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
        setTimeout(() => {
          this.showRegisterModal();
        }, 300);
      }

      switchToLogin() {
        bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
        setTimeout(() => {
          this.showLoginModal();
        }, 300);
      }

      showNotification(message, type = 'info') {
        // 使用备忘录管理器的通知功能
        if (window.memoManager) {
          window.memoManager.showNotification(message, type);
        } else {
          alert(message);
        }
      }

      handleLogout() {
        if (confirm('确定要退出登录吗？')) {
          this.clearAuth();
          this.showNotification('已退出登录', 'info');

          // 刷新页面数据
          if (window.memoManager) {
            window.memoManager.memos.clear();
            window.memoManager.updateCalendarDisplay();
          }
        }
      }
    }

    // 简化的日历类
    class SimpleCalendar {
      constructor() {
        this.currentYear = new Date().getFullYear();
        this.weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        this.months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
        this.memos = new Map();
        this.holidays = new Map();

        this.init();
      }

      init() {
        this.bindEvents();
        this.generateDynamicData();
        this.updateDisplay();
      }

      bindEvents() {
        document.getElementById('prevYear').addEventListener('click', () => {
          this.changeYear(-1);
        });

        document.getElementById('nextYear').addEventListener('click', () => {
          this.changeYear(1);
        });

        document.getElementById('todayBtn').addEventListener('click', () => {
          this.goToToday();
        });

        document.getElementById('addMemoBtn').addEventListener('click', () => {
          console.log('添加备忘录按钮被点击');
          this.openMemoModal();
        });
      }

      generateDynamicData() {
        this.holidays.clear();
        this.memos.clear();

        // 动态生成全年的节假日数据
        for (let month = 1; month <= 12; month++) {
          const daysInMonth = new Date(this.currentYear, month, 0).getDate();

          for (let day = 1; day <= daysInMonth; day++) {
            const dateStr = `${this.currentYear}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const dateInfo = window.lunarCalendar.getDateInfo(this.currentYear, month, day);

            const holidays = [];

            // 法定节假日
            if (dateInfo.legalHoliday) {
              holidays.push({
                name: dateInfo.legalHoliday,
                type: 'legal'
              });
            }

            // 传统节日
            if (dateInfo.traditionalFestival) {
              holidays.push({
                name: dateInfo.traditionalFestival,
                type: 'traditional'
              });
            }

            // 二十四节气
            if (dateInfo.solarTerm) {
              holidays.push({
                name: dateInfo.solarTerm,
                type: 'solar_term'
              });
            }

            if (holidays.length > 0) {
              this.holidays.set(dateStr, holidays);
            }
          }
        }

        // 生成一些示例备忘录
        const sampleMemos = [
          { memo_date: `${this.currentYear}-01-15`, title: '年度总结会议', priority: 'high', category: 'work' },
          { memo_date: `${this.currentYear}-03-15`, title: '体检预约', priority: 'medium', category: 'health' },
          { memo_date: `${this.currentYear}-06-15`, title: '家庭聚餐', priority: 'high', category: 'family' },
          { memo_date: `${this.currentYear}-09-15`, title: '秋游计划', priority: 'medium', category: 'personal' },
          { memo_date: `${this.currentYear}-12-15`, title: '年终购物', priority: 'low', category: 'personal' }
        ];

        sampleMemos.forEach(memo => {
          const date = memo.memo_date;
          if (!this.memos.has(date)) {
            this.memos.set(date, []);
          }
          this.memos.get(date).push(memo);
        });
      }

      getChineseYear(year) {
        return window.lunarCalendar.getYearName(year);
      }

      generateMonthData(month) {
        const year = this.currentYear;
        const firstDay = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0);
        const daysInMonth = lastDay.getDate();
        const startWeekday = firstDay.getDay();
        const today = new Date();

        const days = [];

        // 上个月的日期
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        const prevMonthDays = new Date(prevYear, prevMonth, 0).getDate();

        for (let i = startWeekday - 1; i >= 0; i--) {
          const day = prevMonthDays - i;
          days.push({
            day: day,
            isCurrentMonth: false,
            isToday: false,
            isWeekend: false,
            date: `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
            holidays: [],
            memoCount: 0
          });
        }

        // 当前月的日期
        for (let day = 1; day <= daysInMonth; day++) {
          const date = new Date(year, month - 1, day);
          const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          const isToday = date.toDateString() === today.toDateString();
          const isWeekend = date.getDay() === 0 || date.getDay() === 6;
          const holidays = this.holidays.get(dateStr) || [];
          const memoCount = this.memos.has(dateStr) ? this.memos.get(dateStr).length : 0;

          // 获取农历信息
          let lunarInfo = null;
          try {
            if (window.lunarCalendar) {
              lunarInfo = window.lunarCalendar.getDateInfo(year, month, day);
            }
          } catch (error) {
            console.error('❌ 获取农历信息失败:', error, { year, month, day });
          }

          days.push({
            day: day,
            isCurrentMonth: true,
            isToday: isToday,
            isWeekend: isWeekend,
            date: dateStr,
            holidays: holidays,
            memoCount: memoCount,
            lunarDay: lunarInfo ? lunarInfo.lunar.dayName : '',
            lunarMonth: lunarInfo ? lunarInfo.lunar.monthName : '',
            solarTerm: lunarInfo ? lunarInfo.solarTerm : null
          });
        }

        // 下个月的日期
        const nextMonth = month === 12 ? 1 : month + 1;
        const nextYear = month === 12 ? year + 1 : year;
        const totalCells = 42;
        const remainingCells = totalCells - days.length;

        for (let day = 1; day <= remainingCells && day <= 14; day++) {
          days.push({
            day: day,
            isCurrentMonth: false,
            isToday: false,
            isWeekend: false,
            date: `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
            holidays: [],
            memoCount: 0
          });
        }

        return days;
      }

      generateMonthCard(month) {
        const monthData = this.generateMonthData(month);
        const monthName = this.months[month - 1];

        let html = `
          <div class="col-lg-4 col-md-6">
            <div class="month-card" onclick="alert('点击了${this.currentYear}年${monthName}')">
              <div class="month-header">
                ${this.currentYear}年${monthName}
              </div>

              <div class="weekdays-header">
                ${this.weekdays.map(day => `<div class="weekday-cell">${day}</div>`).join('')}
              </div>

              <div class="days-grid">
        `;

        monthData.forEach(day => {
          const classes = ['day-cell'];

          if (!day.isCurrentMonth) classes.push('other-month');
          if (day.isToday) classes.push('today');
          if (day.isWeekend && day.isCurrentMonth) classes.push('weekend');
          if (day.holidays.length > 0) {
            const primaryHoliday = day.holidays[0];
            classes.push(`holiday-${primaryHoliday.type}`);
          }
          if (day.memoCount > 0) classes.push('has-memo');

          const holidayTag = day.holidays.length > 0 ?
            `<div class="holiday-tag">${day.holidays[0].name}</div>` : '';

          // 农历显示
          const lunarDisplay = day.isCurrentMonth && day.lunarDay ?
            `<div class="lunar-date">${day.lunarDay}</div>` : '';

          // 节气显示
          const solarTermDisplay = day.solarTerm ?
            `<div class="solar-term">${day.solarTerm}</div>` : '';

          html += `
            <div class="${classes.join(' ')}" onclick="event.stopPropagation(); calendar.openMemoModal('${day.date}')">
              <div class="day-number">${day.day}</div>
              ${lunarDisplay}
              ${solarTermDisplay}
              ${holidayTag}
              ${day.memoCount > 0 ? `<div class="memo-indicator">${day.memoCount}</div>` : ''}
            </div>
          `;
        });

        html += `
              </div>
            </div>
          </div>
        `;

        return html;
      }

      generateCalendar() {
        const grid = document.getElementById('calendarGrid');
        let html = '';

        for (let month = 1; month <= 12; month++) {
          html += this.generateMonthCard(month);
        }

        grid.innerHTML = html;
      }

      updateDisplay() {
        document.getElementById('currentYear').textContent = this.currentYear;
        document.getElementById('chineseYear').textContent = this.getChineseYear(this.currentYear);
        this.generateCalendar();
        this.updateStats();
      }

      updateStats() {
        // 获取年度统计信息
        const yearStats = window.lunarCalendar.getYearStats(this.currentYear);

        // 备忘录统计
        let totalMemos = 0;
        let todayMemos = 0;
        const today = new Date().toISOString().split('T')[0];

        this.memos.forEach((memos, date) => {
          totalMemos += memos.length;
          if (date === today) {
            todayMemos = memos.length;
          }
        });

        // 节假日统计
        let legalHolidays = 0;
        let traditionalFestivals = 0;
        let solarTerms = 0;

        this.holidays.forEach((holidays) => {
          holidays.forEach(holiday => {
            switch(holiday.type) {
              case 'legal':
                legalHolidays++;
                break;
              case 'traditional':
                traditionalFestivals++;
                break;
              case 'solar_term':
                solarTerms++;
                break;
            }
          });
        });

        // 更新显示
        document.getElementById('totalMemos').textContent = totalMemos;
        document.getElementById('todayMemos').textContent = todayMemos;
        document.getElementById('holidays').textContent = legalHolidays + traditionalFestivals + solarTerms;
        document.getElementById('weekends').textContent = yearStats.weekends;

        // 更新当前农历信息
        this.updateCurrentLunarInfo();
      }

      updateCurrentLunarInfo() {
        try {
          if (!window.lunarCalendar) {
            console.error('❌ 农历计算库未加载');
            document.getElementById('currentLunarDate').textContent = '农历加载中...';
            document.getElementById('currentSolarTerm').textContent = '节气加载中...';
            return;
          }

          const currentDate = window.lunarCalendar.getCurrentLunarDate();
          console.log('🗓️ 当前农历信息:', currentDate);

          document.getElementById('currentLunarDate').textContent =
            `农历${currentDate.lunar.monthName}${currentDate.lunar.dayName}`;

          // 显示最近的节气
          const nearestTerm = this.findNearestSolarTerm();
          document.getElementById('currentSolarTerm').textContent = nearestTerm;
        } catch (error) {
          console.error('❌ 更新农历信息失败:', error);
          document.getElementById('currentLunarDate').textContent = '农历信息错误';
          document.getElementById('currentSolarTerm').textContent = '节气信息错误';
        }
      }

      findNearestSolarTerm() {
        const now = new Date();
        const currentMonth = now.getMonth() + 1;
        const currentDay = now.getDate();

        // 简化的节气查找
        const terms = window.lunarCalendar.calculateSolarTerms(this.currentYear);
        let nearestTerm = '节气间';

        for (let term of terms) {
          if (term.month === currentMonth && Math.abs(term.day - currentDay) <= 7) {
            if (term.day <= currentDay) {
              nearestTerm = `${term.name}后`;
            } else {
              nearestTerm = `${term.name}前`;
            }
            break;
          }
        }

        return nearestTerm;
      }

      changeYear(delta) {
        this.currentYear += delta;
        this.generateDynamicData();
        this.updateDisplay();
      }

      goToToday() {
        this.currentYear = new Date().getFullYear();
        this.generateDynamicData();
        this.updateDisplay();
      }

      openMemoModal(date = null) {
        if (window.memoManager) {
          window.memoManager.openMemoModal(date);
        }
      }

      openMemoList(date = null) {
        if (window.memoManager) {
          window.memoManager.openMemoList(date);
        }
      }
    }

    // 简化的天气管理器
    class SimpleWeatherManager {
      constructor() {
        this.widget = document.getElementById('weatherWidget');
        this.isVisible = false;
        this.init();
      }

      init() {
        this.bindEvents();
        this.createWeatherContent();
      }

      bindEvents() {
        document.getElementById('weatherBtn').addEventListener('click', () => {
          this.toggle();
        });

        document.getElementById('closeWeather').addEventListener('click', () => {
          this.hide();
        });
      }

      createWeatherContent() {
        const content = this.widget.querySelector('.weather-content');
        content.innerHTML = `
          <div class="current-weather mb-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">北京市</h5>
                <small class="text-muted">${new Date().toLocaleString()}</small>
              </div>
              <div class="text-end">
                <div class="h3 mb-0 text-primary">15°C</div>
                <small class="text-muted">晴</small>
              </div>
            </div>
          </div>

          <div class="weather-details mb-4">
            <div class="row g-3">
              <div class="col-6">
                <div class="detail-item p-2 bg-light rounded">
                  <i class="bi bi-droplet text-info"></i>
                  <span class="ms-2">湿度</span>
                  <strong class="float-end">65%</strong>
                </div>
              </div>
              <div class="col-6">
                <div class="detail-item p-2 bg-light rounded">
                  <i class="bi bi-wind text-success"></i>
                  <span class="ms-2">风速</span>
                  <strong class="float-end">8.5 km/h</strong>
                </div>
              </div>
            </div>
          </div>

          <div class="forecast">
            <h6 class="mb-3">未来三天</h6>
            <div class="row g-2">
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">明天</small>
                    <small class="d-block">1月17日</small>
                    <small class="d-block fw-bold">多云</small>
                    <small class="text-primary">18°/8°</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">后天</small>
                    <small class="d-block">1月18日</small>
                    <small class="d-block fw-bold">晴</small>
                    <small class="text-primary">20°/10°</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">大后天</small>
                    <small class="d-block">1月19日</small>
                    <small class="d-block fw-bold">阴</small>
                    <small class="text-primary">16°/6°</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      }

      toggle() {
        if (this.isVisible) {
          this.hide();
        } else {
          this.show();
        }
      }

      show() {
        this.isVisible = true;
        this.widget.classList.add('show');
      }

      hide() {
        this.isVisible = false;
        this.widget.classList.remove('show');
      }
    }

    // 初始化应用
    let calendar, weatherManager;

    document.addEventListener('DOMContentLoaded', function() {
      console.log('📱 页面加载完成，初始化日历...');

      // 确保主应用区域可见
      const appElement = document.getElementById('app');
      if (appElement) {
        appElement.style.display = 'block';
        appElement.style.visibility = 'visible';
        console.log('✅ 主应用区域已显示');
      } else {
        console.error('❌ 找不到app容器');
      }

      // 隐藏加载屏幕
      const loadingScreen = document.getElementById('loadingScreen');
      if (loadingScreen) {
        loadingScreen.style.display = 'none';
        console.log('✅ 加载屏幕已隐藏');
      } else {
        console.error('❌ 找不到loadingScreen');
      }

      // 初始化用户管理器（优先初始化）
      console.log('🔐 初始化用户管理器...');
      const userManager = new UserManager();
      window.userManager = userManager;
      console.log('✅ 用户管理器初始化完成');

      // 添加调试函数到全局作用域
      window.showLoginButtons = function() {
        console.log('🔧 手动显示登录按钮...');
        const authButtons = document.getElementById('authButtons');
        const userMenu = document.getElementById('userMenu');

        if (authButtons) {
          authButtons.style.display = 'block';
          console.log('✅ 登录按钮已显示');
        } else {
          console.error('❌ 找不到authButtons元素');
        }

        if (userMenu) {
          userMenu.style.display = 'none';
          console.log('✅ 用户菜单已隐藏');
        } else {
          console.error('❌ 找不到userMenu元素');
        }
      };

      // 立即尝试显示登录按钮
      setTimeout(() => {
        console.log('⏰ 延迟显示登录按钮...');
        window.showLoginButtons();
      }, 1000);

      // 初始化日历和天气
      calendar = new SimpleCalendar();
      weatherManager = new SimpleWeatherManager();

      // 完善的备忘录管理系统
      class MemoManager {
        constructor() {
          this.memos = new Map();
          this.currentEditingId = null;
          this.quillEditor = null;
          this.isRichTextMode = false;
          this.mediaRecorder = null;
          this.audioChunks = [];
          this.recordingTimer = null;
          this.recordingStartTime = 0;
          this.uploadedFiles = [];
          this.init();
        }

        async init() {
          this.bindEvents();
          await this.loadMemosFromAPI();
        }

        bindEvents() {
          // 保存备忘录
          document.getElementById('saveMemo').addEventListener('click', () => {
            this.saveMemo();
          });

          // 删除备忘录
          document.getElementById('deleteMemo').addEventListener('click', () => {
            this.deleteMemo();
          });

          // 新建备忘录
          document.getElementById('addNewMemo').addEventListener('click', () => {
            this.openMemoModal();
          });

          // 搜索和筛选
          document.getElementById('memoSearch').addEventListener('input', () => {
            this.filterMemos();
          });

          document.getElementById('memoFilterCategory').addEventListener('change', () => {
            this.filterMemos();
          });

          document.getElementById('memoFilterStatus').addEventListener('change', () => {
            this.filterMemos();
          });

          // 报表功能
          document.getElementById('generateReport').addEventListener('click', () => {
            this.openReportModal();
          });

          document.getElementById('exportMemos').addEventListener('click', () => {
            this.exportMemos();
          });

          document.getElementById('batchDelete').addEventListener('click', () => {
            this.batchDeleteMemos();
          });

          // 多选功能事件
          document.getElementById('selectAllMemos').addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
          });

          document.getElementById('batchDeleteSelected').addEventListener('click', () => {
            this.batchDeleteSelected();
          });

          document.getElementById('batchCompleteSelected').addEventListener('click', () => {
            this.batchCompleteSelected();
          });

          document.getElementById('cancelSelection').addEventListener('click', () => {
            this.cancelSelection();
          });

          // 报表模态框事件
          document.getElementById('reportTimeRange').addEventListener('change', (e) => {
            this.toggleCustomDateRange(e.target.value === 'custom');
          });

          document.getElementById('generateReportBtn').addEventListener('click', () => {
            this.generateReport();
          });

          document.getElementById('exportReportBtn').addEventListener('click', () => {
            this.exportReport();
          });

          document.getElementById('printReportBtn').addEventListener('click', () => {
            this.printReport();
          });

          // 富文本编辑器和多媒体功能事件
          document.getElementById('toggleRichText').addEventListener('click', () => {
            this.toggleRichTextEditor();
          });

          document.getElementById('addImageBtn').addEventListener('click', () => {
            this.toggleImageUpload();
          });

          document.getElementById('voiceRecordBtn').addEventListener('click', () => {
            this.toggleVoiceRecorder();
          });

          // 重复设置事件
          document.getElementById('memoRecurring').addEventListener('change', (e) => {
            this.toggleRecurringOptions(e.target.value);
          });

          // 图片上传事件
          document.getElementById('imageUploadArea').addEventListener('click', () => {
            document.getElementById('imageInput').click();
          });

          document.getElementById('imageInput').addEventListener('change', (e) => {
            this.handleImageUpload(e.target.files);
          });

          // 拖拽上传事件
          const uploadArea = document.getElementById('imageUploadArea');
          uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
          });

          uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
          });

          uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleImageUpload(e.dataTransfer.files);
          });

          // 语音录制事件
          document.getElementById('recordButton').addEventListener('click', () => {
            this.startRecording();
          });

          document.getElementById('stopRecord').addEventListener('click', () => {
            this.stopRecording();
          });

          document.getElementById('playRecord').addEventListener('click', () => {
            this.playRecording();
          });

          // 模态框关闭时重置
          document.getElementById('memoModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm();
          });
        }

        async loadMemosFromAPI() {
          try {
            const headers = {};

            // 添加认证头
            if (window.userManager && window.userManager.token) {
              headers['Authorization'] = `Bearer ${window.userManager.token}`;
            }

            const response = await fetch('/api/memos', { headers });
            if (!response.ok) {
              throw new Error('获取备忘录失败');
            }

            const data = await response.json();
            this.memos.clear();

            data.memos.forEach(memo => {
              // 转换数据库格式到前端格式
              const frontendMemo = {
                id: memo.id.toString(),
                date: memo.memo_date,
                time: memo.memo_time || '',
                title: memo.title,
                content: memo.content || '',
                priority: memo.priority,
                category: memo.category,
                status: memo.status,
                tags: memo.tags ? JSON.parse(memo.tags) : [],
                reminder: memo.reminder_minutes || ''
              };

              this.memos.set(frontendMemo.id, frontendMemo);
            });

          } catch (error) {
            console.error('加载备忘录失败:', error);
            // 如果API失败，加载示例数据
            this.loadSampleMemos();
          }
        }

        loadSampleMemos() {
          const sampleMemos = [
            {
              id: 'memo_1',
              date: '2025-01-20',
              time: '09:00',
              title: '团队会议',
              content: '讨论Q1季度计划和目标',
              priority: 'high',
              category: 'work',
              status: 'pending',
              tags: ['会议', '计划', '重要'],
              reminder: 15
            },
            {
              id: 'memo_2',
              date: '2025-01-25',
              time: '14:30',
              title: '体检预约',
              content: '年度健康体检，记得空腹',
              priority: 'medium',
              category: 'health',
              status: 'pending',
              tags: ['健康', '体检'],
              reminder: 1440
            },
            {
              id: 'memo_3',
              date: '2025-02-14',
              time: '',
              title: '情人节礼物',
              content: '准备情人节惊喜',
              priority: 'medium',
              category: 'personal',
              status: 'pending',
              tags: ['节日', '礼物'],
              reminder: ''
            }
          ];

          sampleMemos.forEach(memo => {
            this.memos.set(memo.id, memo);
          });

          this.updateCalendarDisplay();
        }

        openMemoModal(date = null, memoId = null) {
          this.currentEditingId = memoId;

          if (memoId && this.memos.has(memoId)) {
            // 编辑模式
            const memo = this.memos.get(memoId);
            this.fillForm(memo);
            document.getElementById('memoModalTitle').textContent = '编辑备忘录';
            document.getElementById('deleteMemo').classList.remove('btn-hidden');
          } else {
            // 新建模式
            this.resetForm();
            if (date) {
              document.getElementById('memoDate').value = date;
            }
            document.getElementById('memoModalTitle').textContent = '添加备忘录';
            document.getElementById('deleteMemo').classList.add('btn-hidden');
          }

          const modal = new bootstrap.Modal(document.getElementById('memoModal'));
          modal.show();
        }

        fillForm(memo) {
          document.getElementById('memoDate').value = memo.date;
          document.getElementById('memoTime').value = memo.time || '';
          document.getElementById('memoTitle').value = memo.title;
          document.getElementById('memoContent').value = memo.content || '';
          document.getElementById('memoPriority').value = memo.priority;
          document.getElementById('memoCategory').value = memo.category;
          document.getElementById('memoStatus').value = memo.status;
          document.getElementById('memoReminder').value = memo.reminder || '';
          document.getElementById('memoTags').value = memo.tags ? memo.tags.join(', ') : '';
        }

        resetForm() {
          document.getElementById('memoForm').reset();
          document.getElementById('memoPriority').value = 'medium';
          document.getElementById('memoCategory').value = 'work';
          document.getElementById('memoStatus').value = 'pending';
          document.getElementById('memoDate').value = new Date().toISOString().split('T')[0];
          this.currentEditingId = null;
        }

        async saveMemo() {
          const formData = this.getFormData();

          if (!this.validateForm(formData)) {
            return;
          }

          try {
            const apiData = {
              title: formData.title,
              content: formData.content,
              memoDate: formData.date,
              memoTime: formData.time,
              priority: formData.priority,
              category: formData.category,
              status: formData.status,
              reminderMinutes: formData.reminder ? parseInt(formData.reminder) : null,
              tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : []
            };

            const headers = {
              'Content-Type': 'application/json'
            };

            // 添加认证头
            if (window.userManager && window.userManager.token) {
              headers['Authorization'] = `Bearer ${window.userManager.token}`;
            }

            let response;
            if (this.currentEditingId) {
              // 更新备忘录
              response = await fetch(`/api/memos/${this.currentEditingId}`, {
                method: 'PUT',
                headers: headers,
                body: JSON.stringify(apiData)
              });
            } else {
              // 创建新备忘录
              response = await fetch('/api/memos', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(apiData)
              });
            }

            if (!response.ok) {
              const errorData = await response.json();
              throw new Error(errorData.error || '保存失败');
            }

            const result = await response.json();

            // 重新加载备忘录数据
            await this.loadMemosFromAPI();
            this.updateCalendarDisplay();

            const modal = bootstrap.Modal.getInstance(document.getElementById('memoModal'));
            modal.hide();

            this.showNotification(result.message || '备忘录保存成功！', 'success');

          } catch (error) {
            console.error('保存备忘录失败:', error);
            this.showNotification('保存失败: ' + error.message, 'error');
          }
        }

        getFormData() {
          return {
            date: document.getElementById('memoDate').value,
            time: document.getElementById('memoTime').value,
            title: document.getElementById('memoTitle').value.trim(),
            content: document.getElementById('memoContent').value.trim(),
            priority: document.getElementById('memoPriority').value,
            category: document.getElementById('memoCategory').value,
            status: document.getElementById('memoStatus').value,
            reminder: document.getElementById('memoReminder').value,
            tags: document.getElementById('memoTags').value.trim()
          };
        }

        validateForm(formData) {
          if (!formData.title) {
            this.showNotification('请输入备忘录标题', 'error');
            return false;
          }

          if (!formData.date) {
            this.showNotification('请选择日期', 'error');
            return false;
          }

          return true;
        }

        async deleteMemo() {
          if (!this.currentEditingId) return;

          if (confirm('确定要删除这个备忘录吗？')) {
            try {
              const headers = {};

              // 添加认证头
              if (window.userManager && window.userManager.token) {
                headers['Authorization'] = `Bearer ${window.userManager.token}`;
              }

              const response = await fetch(`/api/memos/${this.currentEditingId}`, {
                method: 'DELETE',
                headers: headers
              });

              if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || '删除失败');
              }

              // 重新加载备忘录数据
              await this.loadMemosFromAPI();
              this.updateCalendarDisplay();

              const modal = bootstrap.Modal.getInstance(document.getElementById('memoModal'));
              modal.hide();

              this.showNotification('备忘录删除成功！', 'success');

            } catch (error) {
              console.error('删除备忘录失败:', error);
              this.showNotification('删除失败: ' + error.message, 'error');
            }
          }
        }

        updateCalendarDisplay() {
          // 更新日历上的备忘录显示
          if (window.calendar) {
            calendar.memos.clear();

            this.memos.forEach(memo => {
              const date = memo.date;
              if (!calendar.memos.has(date)) {
                calendar.memos.set(date, []);
              }
              calendar.memos.get(date).push(memo);
            });

            calendar.generateCalendar();
            calendar.updateStats();
          }
        }

        showNotification(message, type = 'info') {
          const alertClass = type === 'error' ? 'alert-danger' :
                           type === 'success' ? 'alert-success' : 'alert-info';

          const alertDiv = document.createElement('div');
          alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
          alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
          alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
          `;

          document.body.appendChild(alertDiv);

          setTimeout(() => {
            if (alertDiv.parentNode) {
              alertDiv.parentNode.removeChild(alertDiv);
            }
          }, 3000);
        }

        openMemoList(date = null) {
          const modal = new bootstrap.Modal(document.getElementById('memoListModal'));

          if (date) {
            document.getElementById('memoListTitle').textContent = `${this.formatDate(date)} 的备忘录`;
          } else {
            document.getElementById('memoListTitle').textContent = '所有备忘录';
          }

          this.renderMemoList();
          modal.show();
        }

        renderMemoList() {
          const container = document.getElementById('memoListContainer');
          const memos = Array.from(this.memos.values());

          if (memos.length === 0) {
            container.innerHTML = `
              <div class="memo-empty">
                <i class="bi bi-journal-x"></i>
                <h5>暂无备忘录</h5>
                <p>点击"新建"按钮添加第一个备忘录</p>
              </div>
            `;
            return;
          }

          // 按日期排序
          memos.sort((a, b) => new Date(a.date) - new Date(b.date));

          container.innerHTML = memos.map(memo => this.renderMemoItem(memo)).join('');
        }

        renderMemoItem(memo) {
          const priorityIcon = memo.priority === 'high' ? '🔴' :
                              memo.priority === 'medium' ? '🟡' : '🟢';
          const statusIcon = memo.status === 'completed' ? '✅' :
                            memo.status === 'in_progress' ? '🔄' :
                            memo.status === 'cancelled' ? '❌' : '⏳';

          return `
            <div class="memo-item-wrapper" data-memo-id="${memo.id}">
              <div class="memo-item-checkbox">
                <input type="checkbox" class="memo-checkbox" value="${memo.id}" onchange="window.memoManager.handleMemoSelection()">
              </div>
              <div class="memo-item priority-${memo.priority} status-${memo.status}" onclick="window.memoManager.openMemoModal(null, '${memo.id}')">
                <div class="memo-header">
                  <h6 class="memo-title">${memo.title}</h6>
                </div>
                <div class="memo-meta">
                  <span><i class="bi bi-calendar3"></i> ${this.formatDate(memo.date)}</span>
                  ${memo.time ? `<span><i class="bi bi-clock"></i> ${memo.time}</span>` : ''}
                  <span>${priorityIcon} ${memo.priority}</span>
                  <span>${statusIcon} ${memo.status}</span>
                </div>
                ${memo.content ? `<div class="memo-content">${memo.content}</div>` : ''}
                ${memo.tags && memo.tags.length > 0 ? `
                  <div class="memo-tags">
                    ${memo.tags.map(tag => `<span class="memo-tag">#${tag}</span>`).join('')}
                  </div>
                ` : ''}
              </div>
            </div>
          `;
        }

        formatDate(dateStr) {
          const date = new Date(dateStr);
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const day = date.getDate();
          const weekday = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];

          return `${year}年${month}月${day}日 星期${weekday}`;
        }

        filterMemos() {
          // 实现搜索和筛选功能
          const searchTerm = document.getElementById('memoSearch').value.toLowerCase();
          const categoryFilter = document.getElementById('memoFilterCategory').value;
          const statusFilter = document.getElementById('memoFilterStatus').value;

          const memoItems = document.querySelectorAll('.memo-item');

          memoItems.forEach(item => {
            const memo = this.memos.get(item.onclick.toString().match(/'([^']+)'/)[1]);

            const matchesSearch = !searchTerm ||
              memo.title.toLowerCase().includes(searchTerm) ||
              memo.content.toLowerCase().includes(searchTerm) ||
              memo.tags.some(tag => tag.toLowerCase().includes(searchTerm));

            const matchesCategory = !categoryFilter || memo.category === categoryFilter;
            const matchesStatus = !statusFilter || memo.status === statusFilter;

            if (matchesSearch && matchesCategory && matchesStatus) {
              item.style.display = 'block';
            } else {
              item.style.display = 'none';
            }
          });
        }

        // 报表功能
        openReportModal() {
          const modal = new bootstrap.Modal(document.getElementById('reportModal'));
          modal.show();
        }

        toggleCustomDateRange(show) {
          const customRange1 = document.getElementById('customDateRange');
          const customRange2 = document.getElementById('customDateRange2');

          if (show) {
            customRange1.style.display = 'block';
            customRange2.style.display = 'block';
          } else {
            customRange1.style.display = 'none';
            customRange2.style.display = 'none';
          }
        }

        generateReport() {
          const reportType = document.getElementById('reportType').value;
          const timeRange = document.getElementById('reportTimeRange').value;

          let memos = Array.from(this.memos.values());

          // 时间过滤
          memos = this.filterMemosByTimeRange(memos, timeRange);

          let reportContent = '';

          switch (reportType) {
            case 'summary':
              reportContent = this.generateSummaryReport(memos);
              break;
            case 'category':
              reportContent = this.generateCategoryReport(memos);
              break;
            case 'priority':
              reportContent = this.generatePriorityReport(memos);
              break;
            case 'status':
              reportContent = this.generateStatusReport(memos);
              break;
            case 'monthly':
              reportContent = this.generateMonthlyReport(memos);
              break;
          }

          document.getElementById('reportContent').innerHTML = reportContent;
        }

        filterMemosByTimeRange(memos, timeRange) {
          const now = new Date();
          const currentYear = now.getFullYear();
          const currentMonth = now.getMonth();

          switch (timeRange) {
            case 'thisMonth':
              return memos.filter(memo => {
                const memoDate = new Date(memo.date);
                return memoDate.getFullYear() === currentYear &&
                       memoDate.getMonth() === currentMonth;
              });
            case 'lastMonth':
              const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
              const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;
              return memos.filter(memo => {
                const memoDate = new Date(memo.date);
                return memoDate.getFullYear() === lastMonthYear &&
                       memoDate.getMonth() === lastMonth;
              });
            case 'thisYear':
              return memos.filter(memo => {
                const memoDate = new Date(memo.date);
                return memoDate.getFullYear() === currentYear;
              });
            case 'custom':
              const startDate = document.getElementById('reportStartDate').value;
              const endDate = document.getElementById('reportEndDate').value;
              if (startDate && endDate) {
                return memos.filter(memo => {
                  const memoDate = memo.date;
                  return memoDate >= startDate && memoDate <= endDate;
                });
              }
              return memos;
            default:
              return memos;
          }
        }

        generateSummaryReport(memos) {
          const total = memos.length;
          const completed = memos.filter(m => m.status === 'completed').length;
          const pending = memos.filter(m => m.status === 'pending').length;
          const inProgress = memos.filter(m => m.status === 'in_progress').length;
          const cancelled = memos.filter(m => m.status === 'cancelled').length;

          const completionRate = total > 0 ? ((completed / total) * 100).toFixed(1) : 0;

          return `
            <div class="report-summary">
              <h4 class="mb-4">📊 总体统计报表</h4>
              <div class="row">
                <div class="col-md-3">
                  <div class="stat-card bg-primary text-white">
                    <div class="stat-icon"><i class="bi bi-journal-text"></i></div>
                    <div class="stat-info">
                      <h3>${total}</h3>
                      <p>总备忘录数</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-card bg-success text-white">
                    <div class="stat-icon"><i class="bi bi-check-circle"></i></div>
                    <div class="stat-info">
                      <h3>${completed}</h3>
                      <p>已完成</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-card bg-warning text-white">
                    <div class="stat-icon"><i class="bi bi-clock"></i></div>
                    <div class="stat-info">
                      <h3>${pending + inProgress}</h3>
                      <p>进行中</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="stat-card bg-info text-white">
                    <div class="stat-icon"><i class="bi bi-percent"></i></div>
                    <div class="stat-info">
                      <h3>${completionRate}%</h3>
                      <p>完成率</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <h5>📈 状态分布</h5>
                <div class="progress-group">
                  <div class="progress-item">
                    <span>✅ 已完成 (${completed})</span>
                    <div class="progress">
                      <div class="progress-bar bg-success" style="width: ${total > 0 ? (completed/total)*100 : 0}%"></div>
                    </div>
                  </div>
                  <div class="progress-item">
                    <span>⏳ 待办 (${pending})</span>
                    <div class="progress">
                      <div class="progress-bar bg-warning" style="width: ${total > 0 ? (pending/total)*100 : 0}%"></div>
                    </div>
                  </div>
                  <div class="progress-item">
                    <span>🔄 进行中 (${inProgress})</span>
                    <div class="progress">
                      <div class="progress-bar bg-info" style="width: ${total > 0 ? (inProgress/total)*100 : 0}%"></div>
                    </div>
                  </div>
                  <div class="progress-item">
                    <span>❌ 已取消 (${cancelled})</span>
                    <div class="progress">
                      <div class="progress-bar bg-danger" style="width: ${total > 0 ? (cancelled/total)*100 : 0}%"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `;
        }
      }

      // 为MemoManager添加更多报表方法
      MemoManager.prototype.generateCategoryReport = function(memos) {
        const categories = {
          'work': { name: '💼 工作', count: 0, completed: 0 },
          'personal': { name: '👤 个人', count: 0, completed: 0 },
          'family': { name: '👨‍👩‍👧‍👦 家庭', count: 0, completed: 0 },
          'health': { name: '🏥 健康', count: 0, completed: 0 },
          'study': { name: '📚 学习', count: 0, completed: 0 },
          'travel': { name: '✈️ 旅行', count: 0, completed: 0 },
          'other': { name: '📝 其他', count: 0, completed: 0 }
        };

        memos.forEach(memo => {
          if (categories[memo.category]) {
            categories[memo.category].count++;
            if (memo.status === 'completed') {
              categories[memo.category].completed++;
            }
          }
        });

        let html = '<div class="report-category"><h4 class="mb-4">📂 分类统计报表</h4><div class="row">';

        Object.entries(categories).forEach(([key, cat]) => {
          const completionRate = cat.count > 0 ? ((cat.completed / cat.count) * 100).toFixed(1) : 0;
          html += `
            <div class="col-md-6 mb-3">
              <div class="category-card">
                <div class="category-header">
                  <h6>${cat.name}</h6>
                  <span class="badge bg-primary">${cat.count}</span>
                </div>
                <div class="category-stats">
                  <small>已完成: ${cat.completed} / ${cat.count} (${completionRate}%)</small>
                  <div class="progress mt-1">
                    <div class="progress-bar" style="width: ${completionRate}%"></div>
                  </div>
                </div>
              </div>
            </div>
          `;
        });

        html += '</div></div>';
        return html;
      };

      MemoManager.prototype.generatePriorityReport = function(memos) {
        const priorities = {
          'high': { name: '🔴 高优先级', count: 0, completed: 0 },
          'medium': { name: '🟡 中优先级', count: 0, completed: 0 },
          'low': { name: '🟢 低优先级', count: 0, completed: 0 }
        };

        memos.forEach(memo => {
          if (priorities[memo.priority]) {
            priorities[memo.priority].count++;
            if (memo.status === 'completed') {
              priorities[memo.priority].completed++;
            }
          }
        });

        let html = '<div class="report-priority"><h4 class="mb-4">⚡ 优先级统计报表</h4><div class="row">';

        Object.entries(priorities).forEach(([key, priority]) => {
          const completionRate = priority.count > 0 ? ((priority.completed / priority.count) * 100).toFixed(1) : 0;
          const bgColor = key === 'high' ? 'danger' : key === 'medium' ? 'warning' : 'success';

          html += `
            <div class="col-md-4 mb-3">
              <div class="priority-card border-${bgColor}">
                <div class="card-body text-center">
                  <h5 class="text-${bgColor}">${priority.name}</h5>
                  <h2 class="display-4">${priority.count}</h2>
                  <p class="mb-2">已完成: ${priority.completed}</p>
                  <div class="progress">
                    <div class="progress-bar bg-${bgColor}" style="width: ${completionRate}%"></div>
                  </div>
                  <small class="text-muted">${completionRate}% 完成率</small>
                </div>
              </div>
            </div>
          `;
        });

        html += '</div></div>';
        return html;
      };

      MemoManager.prototype.exportMemos = function() {
        const memos = Array.from(this.memos.values());
        const csvContent = this.convertToCSV(memos);
        this.downloadCSV(csvContent, 'memos_export.csv');
      };

      MemoManager.prototype.convertToCSV = function(memos) {
        const headers = ['日期', '时间', '标题', '内容', '优先级', '分类', '状态', '标签'];
        const csvRows = [headers.join(',')];

        memos.forEach(memo => {
          const row = [
            memo.date,
            memo.time || '',
            `"${memo.title}"`,
            `"${memo.content || ''}"`,
            memo.priority,
            memo.category,
            memo.status,
            `"${memo.tags.join(';')}"`
          ];
          csvRows.push(row.join(','));
        });

        return csvRows.join('\n');
      };

      MemoManager.prototype.downloadCSV = function(csvContent, filename) {
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };

      MemoManager.prototype.batchDeleteMemos = function() {
        if (confirm('确定要删除所有已完成的备忘录吗？此操作不可撤销！')) {
          const completedMemos = Array.from(this.memos.entries())
            .filter(([id, memo]) => memo.status === 'completed');

          completedMemos.forEach(([id, memo]) => {
            this.memos.delete(id);
          });

          this.updateCalendarDisplay();
          this.renderMemoList();
          this.showNotification(`已删除 ${completedMemos.length} 个已完成的备忘录`, 'success');
        }
      };

      // 多选功能方法
      MemoManager.prototype.handleMemoSelection = function() {
        const checkboxes = document.querySelectorAll('.memo-checkbox');
        const selectedCheckboxes = document.querySelectorAll('.memo-checkbox:checked');
        const selectAllCheckbox = document.getElementById('selectAllMemos');
        const batchOperationBar = document.getElementById('batchOperationBar');
        const selectedCount = document.getElementById('selectedCount');

        // 更新选中数量
        selectedCount.textContent = selectedCheckboxes.length;

        // 显示或隐藏批量操作栏
        if (selectedCheckboxes.length > 0) {
          batchOperationBar.style.display = 'block';
        } else {
          batchOperationBar.style.display = 'none';
        }

        // 更新全选复选框状态
        if (selectedCheckboxes.length === 0) {
          selectAllCheckbox.indeterminate = false;
          selectAllCheckbox.checked = false;
        } else if (selectedCheckboxes.length === checkboxes.length) {
          selectAllCheckbox.indeterminate = false;
          selectAllCheckbox.checked = true;
        } else {
          selectAllCheckbox.indeterminate = true;
        }
      };

      MemoManager.prototype.toggleSelectAll = function(checked) {
        const checkboxes = document.querySelectorAll('.memo-checkbox');
        checkboxes.forEach(checkbox => {
          checkbox.checked = checked;
        });
        this.handleMemoSelection();
      };

      MemoManager.prototype.batchDeleteSelected = async function() {
        const selectedCheckboxes = document.querySelectorAll('.memo-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
          this.showNotification('请先选择要删除的备忘录', 'warning');
          return;
        }

        if (confirm(`确定要删除选中的 ${selectedIds.length} 个备忘录吗？此操作不可撤销！`)) {
          try {
            // 显示删除进度
            this.showNotification(`正在删除 ${selectedIds.length} 个备忘录...`, 'info');

            // 批量删除API调用
            const deletePromises = selectedIds.map(id => this.deleteMemoFromAPI(id));
            await Promise.all(deletePromises);

            // 从本地内存中删除
            selectedIds.forEach(id => {
              this.memos.delete(id);
            });

            this.updateCalendarDisplay();
            this.renderMemoList();
            this.showNotification(`✅ 已成功删除 ${selectedIds.length} 个备忘录`, 'success');
            this.cancelSelection();

            console.log(`🗑️ 批量删除完成: ${selectedIds.length} 个备忘录`);

          } catch (error) {
            console.error('❌ 批量删除失败:', error);
            this.showNotification('批量删除失败: ' + error.message, 'error');
          }
        }
      };

      MemoManager.prototype.batchCompleteSelected = function() {
        const selectedCheckboxes = document.querySelectorAll('.memo-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
          this.showNotification('请先选择要标记完成的备忘录', 'warning');
          return;
        }

        selectedIds.forEach(id => {
          const memo = this.memos.get(id);
          if (memo) {
            memo.status = 'completed';
            this.memos.set(id, memo);
          }
        });

        this.updateCalendarDisplay();
        this.renderMemoList();
        this.showNotification(`已标记 ${selectedIds.length} 个备忘录为完成`, 'success');
        this.cancelSelection();
      };

      MemoManager.prototype.cancelSelection = function() {
        const checkboxes = document.querySelectorAll('.memo-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllMemos');
        const batchOperationBar = document.getElementById('batchOperationBar');

        checkboxes.forEach(checkbox => {
          checkbox.checked = false;
        });

        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
        batchOperationBar.style.display = 'none';
      };

      MemoManager.prototype.deleteMemoById = async function(id) {
        try {
          // 调用API删除
          await this.deleteMemoFromAPI(id);
          // 从本地内存中删除
          this.memos.delete(id);
          console.log(`🗑️ 已删除备忘录: ${id}`);
        } catch (error) {
          console.error('❌ 删除备忘录失败:', error);
          throw error;
        }
      };

      // 调用API删除备忘录
      MemoManager.prototype.deleteMemoFromAPI = async function(id) {
        try {
          const headers = {
            'Content-Type': 'application/json'
          };

          // 添加认证头
          if (window.userManager && window.userManager.token) {
            headers['Authorization'] = `Bearer ${window.userManager.token}`;
          }

          const response = await fetch(`/api/memos/${id}`, {
            method: 'DELETE',
            headers: headers
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '删除失败');
          }

          const result = await response.json();
          console.log(`📡 API删除成功: ${result.message}`);
          return result;

        } catch (error) {
          console.error('❌ API删除失败:', error);
          throw error;
        }
      };

      // 为MemoManager添加富文本编辑器功能
      MemoManager.prototype.toggleRichTextEditor = function() {
        const richTextDiv = document.getElementById('richTextEditor');
        const textArea = document.getElementById('memoContent');
        const toggleBtn = document.getElementById('toggleRichText');

        if (!this.isRichTextMode) {
          // 切换到富文本模式
          richTextDiv.style.display = 'block';
          textArea.style.display = 'none';

          if (!this.quillEditor) {
            this.quillEditor = new Quill('#quillEditor', {
              theme: 'snow',
              modules: {
                toolbar: [
                  ['bold', 'italic', 'underline', 'strike'],
                  ['blockquote', 'code-block'],
                  [{ 'header': 1 }, { 'header': 2 }],
                  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                  [{ 'script': 'sub'}, { 'script': 'super' }],
                  [{ 'indent': '-1'}, { 'indent': '+1' }],
                  [{ 'direction': 'rtl' }],
                  [{ 'size': ['small', false, 'large', 'huge'] }],
                  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                  [{ 'color': [] }, { 'background': [] }],
                  [{ 'font': [] }],
                  [{ 'align': [] }],
                  ['clean'],
                  ['link', 'image']
                ]
              }
            });
          }

          // 将普通文本内容转移到富文本编辑器
          this.quillEditor.setText(textArea.value);

          toggleBtn.innerHTML = '<i class="bi bi-type"></i>';
          toggleBtn.title = '切换到普通文本';
          this.isRichTextMode = true;

        } else {
          // 切换到普通文本模式
          richTextDiv.style.display = 'none';
          textArea.style.display = 'block';

          // 将富文本内容转移到普通文本
          textArea.value = this.quillEditor.getText();

          toggleBtn.innerHTML = '<i class="bi bi-fonts"></i>';
          toggleBtn.title = '切换富文本编辑';
          this.isRichTextMode = false;
        }
      };

      MemoManager.prototype.toggleImageUpload = function() {
        const uploadArea = document.getElementById('imageUploadArea');
        const isVisible = uploadArea.style.display !== 'none';

        uploadArea.style.display = isVisible ? 'none' : 'block';

        const btn = document.getElementById('addImageBtn');
        if (isVisible) {
          btn.classList.remove('btn-primary');
          btn.classList.add('btn-outline-secondary');
        } else {
          btn.classList.remove('btn-outline-secondary');
          btn.classList.add('btn-primary');
        }
      };

      MemoManager.prototype.handleImageUpload = function(files) {
        const preview = document.getElementById('imagePreview');

        Array.from(files).forEach(file => {
          if (file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
              const imageContainer = document.createElement('div');
              imageContainer.className = 'image-preview-item d-inline-block me-2 mb-2 position-relative';
              imageContainer.innerHTML = `
                <img src="${e.target.result}" class="img-thumbnail" style="max-width: 100px; max-height: 100px;">
                <button type="button" class="btn btn-danger btn-sm position-absolute top-0 end-0"
                        onclick="this.parentElement.remove()" style="transform: translate(50%, -50%);">
                  <i class="bi bi-x"></i>
                </button>
              `;
              preview.appendChild(imageContainer);
            };
            reader.readAsDataURL(file);

            // 保存文件引用
            this.uploadedFiles.push(file);
          }
        });
      };

      MemoManager.prototype.toggleVoiceRecorder = function() {
        const recorder = document.getElementById('voiceRecorder');
        const isVisible = recorder.style.display !== 'none';

        recorder.style.display = isVisible ? 'none' : 'block';

        const btn = document.getElementById('voiceRecordBtn');
        if (isVisible) {
          btn.classList.remove('btn-primary');
          btn.classList.add('btn-outline-secondary');
        } else {
          btn.classList.remove('btn-outline-secondary');
          btn.classList.add('btn-primary');
        }
      };

      MemoManager.prototype.startRecording = async function() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          this.mediaRecorder = new MediaRecorder(stream);
          this.audioChunks = [];

          this.mediaRecorder.ondataavailable = (event) => {
            this.audioChunks.push(event.data);
          };

          this.mediaRecorder.onstop = () => {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/wav' });
            this.createAudioPreview(audioBlob);
          };

          this.mediaRecorder.start();
          this.recordingStartTime = Date.now();

          // 更新UI
          document.getElementById('recordButton').style.display = 'none';
          document.getElementById('stopRecord').style.display = 'inline-block';
          document.getElementById('voiceRecorder').classList.add('recording');

          // 开始计时
          this.recordingTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
            const seconds = (elapsed % 60).toString().padStart(2, '0');
            document.getElementById('recordTime').textContent = `${minutes}:${seconds}`;

            // 模拟音频波形
            const wave = document.getElementById('audioWave');
            wave.style.width = `${Math.random() * 100}%`;
          }, 100);

        } catch (error) {
          console.error('录音失败:', error);
          this.showNotification('录音功能需要麦克风权限', 'error');
        }
      };

      MemoManager.prototype.stopRecording = function() {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop();
          this.mediaRecorder.stream.getTracks().forEach(track => track.stop());

          // 清除计时器
          clearInterval(this.recordingTimer);

          // 更新UI
          document.getElementById('recordButton').style.display = 'inline-block';
          document.getElementById('stopRecord').style.display = 'none';
          document.getElementById('playRecord').style.display = 'inline-block';
          document.getElementById('voiceRecorder').classList.remove('recording');
          document.getElementById('audioWave').style.width = '0%';
        }
      };

      MemoManager.prototype.createAudioPreview = function(audioBlob) {
        const preview = document.getElementById('audioPreview');
        const audioContainer = document.createElement('div');
        audioContainer.className = 'audio-preview-item d-flex align-items-center p-2 bg-light rounded mb-2';

        const audioUrl = URL.createObjectURL(audioBlob);
        audioContainer.innerHTML = `
          <i class="bi bi-mic-fill me-2"></i>
          <audio controls class="flex-grow-1 me-2">
            <source src="${audioUrl}" type="audio/wav">
          </audio>
          <button type="button" class="btn btn-danger btn-sm" onclick="this.parentElement.remove()">
            <i class="bi bi-trash"></i>
          </button>
        `;

        preview.appendChild(audioContainer);

        // 保存音频文件
        this.uploadedFiles.push(audioBlob);
      };

      MemoManager.prototype.toggleRecurringOptions = function(value) {
        const options = document.getElementById('recurringOptions');
        const weeklyOptions = document.getElementById('weeklyOptions');

        if (value && value !== '') {
          options.style.display = 'block';

          // 根据选择显示相应选项
          if (value === 'weekly') {
            weeklyOptions.style.display = 'block';
          } else {
            weeklyOptions.style.display = 'none';
          }

          // 设置默认值
          const unit = document.getElementById('recurringUnit');
          switch (value) {
            case 'daily':
              unit.value = 'days';
              break;
            case 'weekly':
              unit.value = 'weeks';
              break;
            case 'monthly':
              unit.value = 'months';
              break;
            case 'yearly':
              unit.value = 'years';
              break;
          }
        } else {
          options.style.display = 'none';
          weeklyOptions.style.display = 'none';
        }
      };

      // 为UserManager添加更多方法
      UserManager.prototype.handleLogin = async function() {
        // 获取表单数据并去除空格
        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        console.log('🔍 登录表单数据:', { username, password: '***', rememberMe });

        // 详细的表单验证
        const validationErrors = [];

        if (!username) {
          validationErrors.push('用户名或邮箱不能为空');
          this.highlightField('loginUsername');
        }
        if (!password) {
          validationErrors.push('密码不能为空');
          this.highlightField('loginPassword');
        }

        if (validationErrors.length > 0) {
          this.showNotification(`请填写以下必填字段：\n• ${validationErrors.join('\n• ')}`, 'warning');
          return;
        }

        // 清除字段高亮
        this.clearFieldHighlight('loginUsername');
        this.clearFieldHighlight('loginPassword');

        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password, rememberMe })
          });

          const data = await response.json();

          if (response.ok) {
            console.log('✅ 登录成功，服务器响应:', data);

            this.token = data.token;
            localStorage.setItem('authToken', this.token);
            this.setCurrentUser(data.user);

            console.log('🔍 登录后页面状态检查:');
            console.log('- app容器:', document.getElementById('app'));
            console.log('- app容器样式:', window.getComputedStyle(document.getElementById('app')).display);
            console.log('- loadingScreen:', document.getElementById('loadingScreen'));
            console.log('- loadingScreen样式:', window.getComputedStyle(document.getElementById('loadingScreen')).display);
            console.log('- userMenu:', document.getElementById('userMenu'));
            console.log('- authButtons:', document.getElementById('authButtons'));

            bootstrap.Modal.getInstance(document.getElementById('loginModal')).hide();
            this.showNotification(`欢迎回来，${data.user.full_name || data.user.username}！`, 'success');

            // 确保主应用区域可见
            const appElement = document.getElementById('app');
            if (appElement) {
              appElement.style.display = 'block';
              appElement.style.visibility = 'visible';
              console.log('✅ 确保app容器可见');
            }

            // 确保加载屏幕隐藏
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
              loadingScreen.style.display = 'none';
              console.log('✅ 隐藏加载屏幕');
            }

            // 清空表单
            document.getElementById('loginForm').reset();

            // 重新加载用户的备忘录数据
            if (window.memoManager) {
              console.log('🔄 重新加载备忘录数据...');
              await window.memoManager.loadMemosFromAPI();
            } else {
              console.log('⚠️ memoManager未初始化');
            }

          } else {
            this.showNotification(data.error || '登录失败', 'error');
          }
        } catch (error) {
          console.error('登录错误:', error);
          this.showNotification('登录失败，请检查网络连接', 'error');
        }
      };

      UserManager.prototype.handleRegister = async function() {
        console.log('🚀 开始处理注册...');

        // 等待一小段时间确保DOM完全加载
        await new Promise(resolve => setTimeout(resolve, 100));

        // 获取表单元素
        const usernameEl = document.getElementById('registerUsername');
        const emailEl = document.getElementById('registerEmail');
        const fullNameEl = document.getElementById('registerFullName');
        const passwordEl = document.getElementById('registerPassword');
        const confirmPasswordEl = document.getElementById('confirmPassword');

        console.log('📋 表单元素检查:', {
          usernameEl: !!usernameEl,
          emailEl: !!emailEl,
          fullNameEl: !!fullNameEl,
          passwordEl: !!passwordEl,
          confirmPasswordEl: !!confirmPasswordEl
        });

        if (!usernameEl || !emailEl || !fullNameEl || !passwordEl || !confirmPasswordEl) {
          console.error('❌ 表单元素未找到');
          this.showNotification('表单初始化错误，请刷新页面重试', 'error');
          return;
        }

        // 获取原始值（不去除空格，先看看原始数据）
        const usernameRaw = usernameEl.value;
        const emailRaw = emailEl.value;
        const fullNameRaw = fullNameEl.value;
        const passwordRaw = passwordEl.value;
        const confirmPasswordRaw = confirmPasswordEl.value;

        console.log('🔍 原始表单数据:', {
          usernameRaw: `"${usernameRaw}"`,
          emailRaw: `"${emailRaw}"`,
          fullNameRaw: `"${fullNameRaw}"`,
          passwordRaw: passwordRaw ? `"${passwordRaw}"` : '(空)',
          confirmPasswordRaw: confirmPasswordRaw ? `"${confirmPasswordRaw}"` : '(空)',
          rawLengths: {
            username: usernameRaw.length,
            email: emailRaw.length,
            fullName: fullNameRaw.length,
            password: passwordRaw.length,
            confirmPassword: confirmPasswordRaw.length
          }
        });

        // 获取表单数据并去除空格
        const username = usernameRaw.trim();
        const email = emailRaw.trim();
        const fullName = fullNameRaw.trim();
        const password = passwordRaw;
        const confirmPassword = confirmPasswordRaw;

        console.log('🔍 处理后表单数据:', {
          username: `"${username}"`,
          email: `"${email}"`,
          fullName: `"${fullName}"`,
          password: password ? '***' : '(空)',
          confirmPassword: confirmPassword ? '***' : '(空)',
          lengths: {
            username: username.length,
            email: email.length,
            fullName: fullName.length,
            password: password.length,
            confirmPassword: confirmPassword.length
          }
        });

        // 简化的表单验证 - 逐个检查
        console.log('🔍 开始逐个验证字段...');

        const validationErrors = [];

        // 用户名验证
        console.log(`🔍 验证用户名: "${username}", 长度: ${username.length}, 类型: ${typeof username}`);
        if (username === '' || username.length === 0) {
          validationErrors.push('用户名不能为空');
          this.highlightField('registerUsername');
          console.log('❌ 用户名验证失败');
        } else {
          console.log('✅ 用户名验证通过');
        }

        // 邮箱验证
        console.log(`🔍 验证邮箱: "${email}", 长度: ${email.length}, 类型: ${typeof email}`);
        if (email === '' || email.length === 0) {
          validationErrors.push('邮箱不能为空');
          this.highlightField('registerEmail');
          console.log('❌ 邮箱验证失败');
        } else {
          console.log('✅ 邮箱验证通过');
        }

        // 姓名验证
        console.log(`🔍 验证姓名: "${fullName}", 长度: ${fullName.length}, 类型: ${typeof fullName}`);
        if (fullName === '' || fullName.length === 0) {
          validationErrors.push('姓名不能为空');
          this.highlightField('registerFullName');
          console.log('❌ 姓名验证失败');
        } else {
          console.log('✅ 姓名验证通过');
        }

        // 密码验证
        console.log(`🔍 验证密码: 长度: ${password.length}, 类型: ${typeof password}`);
        if (password === '' || password.length === 0) {
          validationErrors.push('密码不能为空');
          this.highlightField('registerPassword');
          console.log('❌ 密码验证失败');
        } else {
          console.log('✅ 密码验证通过');
        }

        // 确认密码验证
        console.log(`🔍 验证确认密码: 长度: ${confirmPassword.length}, 类型: ${typeof confirmPassword}`);
        if (confirmPassword === '' || confirmPassword.length === 0) {
          validationErrors.push('确认密码不能为空');
          this.highlightField('confirmPassword');
          console.log('❌ 确认密码验证失败');
        } else {
          console.log('✅ 确认密码验证通过');
        }

        // 如果有空字段，显示具体错误
        if (validationErrors.length > 0) {
          console.log('❌ 表单验证失败，错误列表:', validationErrors);
          console.log('❌ 失败的字段详情:', {
            username: { value: `"${username}"`, length: username.length, empty: username === '' },
            email: { value: `"${email}"`, length: email.length, empty: email === '' },
            fullName: { value: `"${fullName}"`, length: fullName.length, empty: fullName === '' },
            password: { length: password.length, empty: password === '' },
            confirmPassword: { length: confirmPassword.length, empty: confirmPassword === '' }
          });
          this.showNotification(`请填写以下必填字段：\n• ${validationErrors.join('\n• ')}`, 'warning');
          return;
        }

        console.log('✅ 所有基础字段验证通过');

        // 用户名格式验证
        if (username.length < 3 || username.length > 20) {
          this.showNotification('用户名长度必须在3-20个字符之间', 'warning');
          this.highlightField('registerUsername');
          return;
        }

        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
          this.showNotification('用户名只能包含字母、数字和下划线', 'warning');
          this.highlightField('registerUsername');
          return;
        }

        // 邮箱格式验证
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          this.showNotification('请输入有效的邮箱地址', 'warning');
          this.highlightField('registerEmail');
          return;
        }

        // 密码验证
        if (password.length < 6) {
          this.showNotification('密码至少需要6个字符', 'warning');
          this.highlightField('registerPassword');
          return;
        }

        if (password !== confirmPassword) {
          this.showNotification('两次输入的密码不一致', 'warning');
          this.highlightField('confirmPassword');
          return;
        }

        // 清除所有字段的错误高亮
        this.clearFieldHighlights();

        try {
          const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, email, fullName, password })
          });

          const data = await response.json();

          if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('registerModal')).hide();
            this.showNotification('注册成功！请登录', 'success');

            // 清空表单
            document.getElementById('registerForm').reset();

            // 自动显示登录框
            setTimeout(() => {
              this.showLoginModal();
              document.getElementById('loginUsername').value = username;
            }, 1000);

          } else {
            this.showNotification(data.error || '注册失败', 'error');
          }
        } catch (error) {
          console.error('注册错误:', error);
          this.showNotification('注册失败，请检查网络连接', 'error');
        }
      };

      UserManager.prototype.showProfileModal = async function() {
        if (!this.currentUser) return;

        try {
          // 获取最新的用户信息
          const response = await fetch('/api/auth/me', {
            headers: {
              'Authorization': `Bearer ${this.token}`
            }
          });

          if (response.ok) {
            const data = await response.json();
            const user = data.user;

            // 填充表单
            document.getElementById('profileUsername').value = user.username;
            document.getElementById('profileEmail').value = user.email;
            document.getElementById('profileFullName').value = user.full_name || '';
            document.getElementById('profileCreatedAt').value = new Date(user.created_at).toLocaleString();
            document.getElementById('profileLastLogin').value = user.last_login ? new Date(user.last_login).toLocaleString() : '从未登录';

            const modal = new bootstrap.Modal(document.getElementById('profileModal'));
            modal.show();
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          this.showNotification('获取用户信息失败', 'error');
        }
      };

      UserManager.prototype.updateProfile = async function() {
        const email = document.getElementById('profileEmail').value;
        const fullName = document.getElementById('profileFullName').value;

        if (!email || !fullName) {
          this.showNotification('请填写邮箱和姓名', 'warning');
          return;
        }

        try {
          const response = await fetch('/api/auth/profile', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({ email, fullName })
          });

          const data = await response.json();

          if (response.ok) {
            this.currentUser = { ...this.currentUser, email, full_name: fullName };
            this.updateUserDisplay();

            bootstrap.Modal.getInstance(document.getElementById('profileModal')).hide();
            this.showNotification('个人资料更新成功', 'success');
          } else {
            this.showNotification(data.error || '更新失败', 'error');
          }
        } catch (error) {
          console.error('更新个人资料失败:', error);
          this.showNotification('更新失败，请检查网络连接', 'error');
        }
      };

      UserManager.prototype.showChangePasswordModal = function() {
        const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
        modal.show();
      };

      // 字段验证辅助方法
      UserManager.prototype.highlightField = function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
          field.classList.add('is-invalid');
          field.style.borderColor = '#dc3545';
          field.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';

          // 3秒后自动清除高亮
          setTimeout(() => {
            this.clearFieldHighlight(fieldId);
          }, 3000);
        }
      };

      UserManager.prototype.clearFieldHighlight = function(fieldId) {
        const field = document.getElementById(fieldId);
        if (field) {
          field.classList.remove('is-invalid');
          field.style.borderColor = '';
          field.style.boxShadow = '';
        }
      };

      UserManager.prototype.clearFieldHighlights = function() {
        const fields = ['registerUsername', 'registerEmail', 'registerFullName', 'registerPassword', 'confirmPassword'];
        fields.forEach(fieldId => {
          this.clearFieldHighlight(fieldId);
        });
      };

      UserManager.prototype.changePassword = async function() {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        if (!currentPassword || !newPassword || !confirmNewPassword) {
          this.showNotification('请填写所有密码字段', 'warning');
          return;
        }

        if (newPassword !== confirmNewPassword) {
          this.showNotification('新密码两次输入不一致', 'warning');
          return;
        }

        if (newPassword.length < 6) {
          this.showNotification('新密码至少需要6个字符', 'warning');
          return;
        }

        try {
          const response = await fetch('/api/auth/change-password', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({ currentPassword, newPassword })
          });

          const data = await response.json();

          if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
            document.getElementById('changePasswordForm').reset();
            this.showNotification('密码修改成功', 'success');
          } else {
            this.showNotification(data.error || '密码修改失败', 'error');
          }
        } catch (error) {
          console.error('修改密码失败:', error);
          this.showNotification('修改密码失败，请检查网络连接', 'error');
        }
      };

      // 调试函数
      window.debugRegisterForm = function() {
        console.log('🔧 开始调试注册表单...');

        const elements = {
          username: document.getElementById('registerUsername'),
          email: document.getElementById('registerEmail'),
          fullName: document.getElementById('registerFullName'),
          password: document.getElementById('registerPassword'),
          confirmPassword: document.getElementById('confirmPassword')
        };

        console.log('📋 表单元素状态:', {
          username: { exists: !!elements.username, value: elements.username?.value || 'N/A' },
          email: { exists: !!elements.email, value: elements.email?.value || 'N/A' },
          fullName: { exists: !!elements.fullName, value: elements.fullName?.value || 'N/A' },
          password: { exists: !!elements.password, hasValue: !!(elements.password?.value) },
          confirmPassword: { exists: !!elements.confirmPassword, hasValue: !!(elements.confirmPassword?.value) }
        });

        // 检查表单是否在模态框中
        const modal = document.getElementById('registerModal');
        const isModalVisible = modal && modal.classList.contains('show');
        console.log('📱 模态框状态:', { exists: !!modal, visible: isModalVisible });

        if (!isModalVisible) {
          console.log('⚠️ 注册模态框未打开，请先点击注册按钮');
          alert('请先点击注册按钮打开注册表单');
          return;
        }

        // 手动触发验证
        if (window.userManager) {
          console.log('🚀 手动触发注册验证...');
          window.userManager.handleRegister();
        } else {
          console.log('❌ UserManager未初始化');
        }
      };

      window.fillTestData = function() {
        console.log('📝 填充测试数据...');

        const elements = {
          username: document.getElementById('registerUsername'),
          email: document.getElementById('registerEmail'),
          fullName: document.getElementById('registerFullName'),
          password: document.getElementById('registerPassword'),
          confirmPassword: document.getElementById('confirmPassword')
        };

        if (elements.username) elements.username.value = 'testuser123';
        if (elements.email) elements.email.value = '<EMAIL>';
        if (elements.fullName) elements.fullName.value = '测试用户';
        if (elements.password) elements.password.value = '123456';
        if (elements.confirmPassword) elements.confirmPassword.value = '123456';

        console.log('✅ 测试数据填充完成');

        // 触发input事件以确保表单知道值已更改
        Object.values(elements).forEach(el => {
          if (el) {
            el.dispatchEvent(new Event('input', { bubbles: true }));
            el.dispatchEvent(new Event('change', { bubbles: true }));
          }
        });
      };

      // 初始化备忘录管理器（用户管理器已在DOMContentLoaded中初始化）
      const memoManager = new MemoManager();
      window.memoManager = memoManager; // 将memoManager设置为全局变量

      // 绑定备忘录列表按钮
      document.getElementById('memoListBtn').addEventListener('click', () => {
        memoManager.openMemoList();
      });

      // 绑定添加备忘录按钮（全局绑定）
      const addMemoBtn = document.getElementById('addMemoBtn');
      if (addMemoBtn) {
        addMemoBtn.addEventListener('click', () => {
          console.log('🎯 添加备忘录按钮被点击');
          if (window.memoManager) {
            window.memoManager.openMemoModal();
          } else {
            console.error('❌ memoManager 未找到');
          }
        });
        console.log('✅ 添加备忘录按钮事件已绑定');
      } else {
        console.error('❌ 添加备忘录按钮未找到');
      }

      // 绑定今天按钮（全局绑定）
      const todayBtn = document.getElementById('todayBtn');
      if (todayBtn) {
        todayBtn.addEventListener('click', () => {
          console.log('🎯 今天按钮被点击');
          // 可以添加跳转到今天的逻辑
          const today = new Date();
          const year = today.getFullYear();
          console.log(`跳转到 ${year} 年`);
        });
        console.log('✅ 今天按钮事件已绑定');
      }
    });
  </script>
</body>
</html>
