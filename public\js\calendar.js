/**
 * 日历核心功能模块
 */
class Calendar {
  constructor() {
    this.currentYear = new Date().getFullYear();
    this.weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    this.months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    this.memos = new Map();
    this.holidays = new Map();
    
    this.init();
  }

  /**
   * 初始化日历
   */
  init() {
    this.bindEvents();
    this.loadData();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 年份切换
    document.getElementById('prevYear').addEventListener('click', () => {
      this.changeYear(-1);
    });
    
    document.getElementById('nextYear').addEventListener('click', () => {
      this.changeYear(1);
    });
    
    // 今天按钮
    document.getElementById('todayBtn').addEventListener('click', () => {
      this.goToToday();
    });
  }

  /**
   * 加载数据
   */
  async loadData() {
    try {
      // 加载节假日数据
      await this.loadHolidays();

      // 加载备忘录数据
      await this.loadMemos();

    } catch (error) {
      console.error('加载数据失败:', error);
      // 即使加载失败，也生成基本的示例数据
      this.generateSampleData();
    } finally {
      // 无论如何都要更新显示
      this.updateDisplay();
    }
  }

  /**
   * 加载节假日数据
   */
  async loadHolidays() {
    try {
      const response = await window.api.getHolidaysByYear(this.currentYear);
      this.holidays.clear();
      
      // 处理所有类型的节假日
      Object.values(response.holidays).forEach(holidayList => {
        holidayList.forEach(holiday => {
          const date = holiday.holiday_date;
          if (!this.holidays.has(date)) {
            this.holidays.set(date, []);
          }
          this.holidays.get(date).push(holiday);
        });
      });
      
    } catch (error) {
      console.error('加载节假日失败:', error);
    }
  }

  /**
   * 加载备忘录数据
   */
  async loadMemos() {
    try {
      const response = await window.api.getMemos({ year: this.currentYear });
      this.memos.clear();
      
      response.memos.forEach(memo => {
        const date = memo.memo_date;
        if (!this.memos.has(date)) {
          this.memos.set(date, []);
        }
        this.memos.get(date).push(memo);
      });
      
    } catch (error) {
      console.error('加载备忘录失败:', error);
      // 使用模拟数据
      this.generateSampleMemos();
    }
  }

  /**
   * 生成示例数据
   */
  generateSampleData() {
    this.generateSampleHolidays();
    this.generateSampleMemos();
  }

  /**
   * 生成示例节假日数据
   */
  generateSampleHolidays() {
    const sampleHolidays = [
      { holiday_date: `${this.currentYear}-01-01`, name: '元旦', type: 'legal' },
      { holiday_date: `${this.currentYear}-02-10`, name: '春节', type: 'legal' },
      { holiday_date: `${this.currentYear}-04-05`, name: '清明节', type: 'legal' },
      { holiday_date: `${this.currentYear}-05-01`, name: '劳动节', type: 'legal' },
      { holiday_date: `${this.currentYear}-06-02`, name: '端午节', type: 'legal' },
      { holiday_date: `${this.currentYear}-10-01`, name: '国庆节', type: 'legal' },
      { holiday_date: `${this.currentYear}-02-14`, name: '情人节', type: 'traditional' },
      { holiday_date: `${this.currentYear}-03-08`, name: '妇女节', type: 'traditional' },
      { holiday_date: `${this.currentYear}-06-01`, name: '儿童节', type: 'traditional' },
      { holiday_date: `${this.currentYear}-09-10`, name: '教师节', type: 'traditional' },
      { holiday_date: `${this.currentYear}-12-25`, name: '圣诞节', type: 'traditional' },
      { holiday_date: `${this.currentYear}-02-04`, name: '立春', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-03-20`, name: '春分', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-05-05`, name: '立夏', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-06-21`, name: '夏至', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-08-07`, name: '立秋', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-09-23`, name: '秋分', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-11-07`, name: '立冬', type: 'solar_term' },
      { holiday_date: `${this.currentYear}-12-22`, name: '冬至', type: 'solar_term' }
    ];

    sampleHolidays.forEach(holiday => {
      const date = holiday.holiday_date;
      if (!this.holidays.has(date)) {
        this.holidays.set(date, []);
      }
      this.holidays.get(date).push(holiday);
    });
  }

  /**
   * 生成示例备忘录数据
   */
  generateSampleMemos() {
    const sampleMemos = [
      { memo_date: `${this.currentYear}-01-15`, title: '项目会议', priority: 'high', category: 'work' },
      { memo_date: `${this.currentYear}-02-14`, title: '情人节', priority: 'medium', category: 'personal' },
      { memo_date: `${this.currentYear}-03-08`, title: '妇女节', priority: 'medium', category: 'family' },
      { memo_date: `${this.currentYear}-05-01`, title: '劳动节假期', priority: 'low', category: 'personal' },
      { memo_date: `${this.currentYear}-06-01`, title: '儿童节', priority: 'high', category: 'family' },
      { memo_date: `${this.currentYear}-09-10`, title: '教师节', priority: 'medium', category: 'personal' },
      { memo_date: `${this.currentYear}-12-25`, title: '圣诞节', priority: 'medium', category: 'personal' }
    ];

    sampleMemos.forEach(memo => {
      const date = memo.memo_date;
      if (!this.memos.has(date)) {
        this.memos.set(date, []);
      }
      this.memos.get(date).push(memo);
    });
  }

  /**
   * 获取干支纪年
   */
  getChineseYear(year) {
    const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
    
    const baseYear = 1984; // 甲子年
    const yearOffset = year - baseYear;
    const heavenlyIndex = ((yearOffset % 10) + 10) % 10;
    const earthlyIndex = ((yearOffset % 12) + 12) % 12;
    
    return heavenlyStems[heavenlyIndex] + earthlyBranches[earthlyIndex] + zodiacAnimals[earthlyIndex] + '年';
  }

  /**
   * 获取农历日期（简化版）
   */
  getLunarDate(year, month, day) {
    const lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    const lunarDays = [
      '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];
    
    const date = new Date(year, month - 1, day);
    const startOfYear = new Date(year, 0, 1);
    const dayOfYear = Math.floor((date - startOfYear) / (1000 * 60 * 60 * 24));
    
    const lunarMonthIndex = Math.floor((dayOfYear + 15) / 30) % 12;
    const lunarDayIndex = Math.floor((dayOfYear + 15) % 30);
    
    return lunarMonths[lunarMonthIndex] + lunarDays[Math.min(lunarDayIndex, 29)];
  }

  /**
   * 生成月份日历数据
   */
  generateMonthData(month) {
    const year = this.currentYear;
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    const startWeekday = firstDay.getDay();
    const today = new Date();
    
    const days = [];
    
    // 上个月的日期
    const prevMonth = month === 1 ? 12 : month - 1;
    const prevYear = month === 1 ? year - 1 : year;
    const prevMonthDays = new Date(prevYear, prevMonth, 0).getDate();
    
    for (let i = startWeekday - 1; i >= 0; i--) {
      const day = prevMonthDays - i;
      days.push({
        day: day,
        isCurrentMonth: false,
        isToday: false,
        isWeekend: false,
        date: `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
        lunarDate: '',
        holidays: [],
        memoCount: 0
      });
    }
    
    // 当前月的日期
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
      const isToday = date.toDateString() === today.toDateString();
      const isWeekend = date.getDay() === 0 || date.getDay() === 6;
      const holidays = this.holidays.get(dateStr) || [];
      const lunarDate = this.getLunarDate(year, month, day);
      const memoCount = this.memos.has(dateStr) ? this.memos.get(dateStr).length : 0;
      
      days.push({
        day: day,
        isCurrentMonth: true,
        isToday: isToday,
        isWeekend: isWeekend,
        date: dateStr,
        lunarDate: lunarDate,
        holidays: holidays,
        memoCount: memoCount
      });
    }
    
    // 下个月的日期
    const nextMonth = month === 12 ? 1 : month + 1;
    const nextYear = month === 12 ? year + 1 : year;
    const totalCells = 42; // 6行 × 7列
    const remainingCells = totalCells - days.length;
    
    for (let day = 1; day <= remainingCells && day <= 14; day++) {
      days.push({
        day: day,
        isCurrentMonth: false,
        isToday: false,
        isWeekend: false,
        date: `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
        lunarDate: '',
        holidays: [],
        memoCount: 0
      });
    }
    
    return days;
  }

  /**
   * 生成月份卡片HTML
   */
  generateMonthCard(month) {
    const monthData = this.generateMonthData(month);
    const monthName = this.months[month - 1];
    
    let html = `
      <div class="col-lg-4 col-md-6">
        <div class="month-card" onclick="calendar.openMonthDetail(${month})">
          <div class="month-header">
            ${this.currentYear}年${monthName}
          </div>
          
          <div class="weekdays-header">
            ${this.weekdays.map(day => `<div class="weekday-cell">${day}</div>`).join('')}
          </div>
          
          <div class="days-grid">
    `;
    
    monthData.forEach(day => {
      const classes = ['day-cell'];
      
      if (!day.isCurrentMonth) classes.push('other-month');
      if (day.isToday) classes.push('today');
      if (day.isWeekend && day.isCurrentMonth) classes.push('weekend');
      if (day.holidays.length > 0) {
        const primaryHoliday = day.holidays[0];
        classes.push(`holiday-${primaryHoliday.type}`);
      }
      if (day.memoCount > 0) classes.push('has-memo');
      
      const holidayTag = day.holidays.length > 0 ? 
        `<div class="holiday-tag">${day.holidays[0].name}</div>` : '';
      
      html += `
        <div class="${classes.join(' ')}" onclick="event.stopPropagation(); calendar.openMemoModal('${day.date}')">
          <div class="day-number">${day.day}</div>
          ${day.isCurrentMonth && day.lunarDate ? `<div class="lunar-date">${day.lunarDate}</div>` : ''}
          ${holidayTag}
          ${day.memoCount > 0 ? `<div class="memo-indicator">${day.memoCount}</div>` : ''}
        </div>
      `;
    });
    
    html += `
          </div>
        </div>
      </div>
    `;
    
    return html;
  }

  /**
   * 生成整个日历
   */
  generateCalendar() {
    const grid = document.getElementById('calendarGrid');
    let html = '';
    
    for (let month = 1; month <= 12; month++) {
      html += this.generateMonthCard(month);
    }
    
    grid.innerHTML = html;
  }

  /**
   * 更新显示
   */
  updateDisplay() {
    document.getElementById('currentYear').textContent = this.currentYear;
    document.getElementById('chineseYear').textContent = this.getChineseYear(this.currentYear);
    this.generateCalendar();
    this.updateStats();
  }

  /**
   * 切换年份
   */
  changeYear(delta) {
    this.currentYear += delta;
    this.loadData();
  }

  /**
   * 跳转到今天
   */
  goToToday() {
    this.currentYear = new Date().getFullYear();
    this.loadData();
  }

  /**
   * 更新统计信息
   */
  updateStats() {
    let totalMemos = 0;
    let todayMemos = 0;
    const today = new Date().toISOString().split('T')[0];
    
    this.memos.forEach((memos, date) => {
      totalMemos += memos.length;
      if (date === today) {
        todayMemos = memos.length;
      }
    });
    
    document.getElementById('totalMemos').textContent = totalMemos;
    document.getElementById('todayMemos').textContent = todayMemos;
    
    // 统计节假日
    let holidayCount = 0;
    this.holidays.forEach((holidays) => {
      holidayCount += holidays.filter(h => h.type === 'legal').length;
    });
    document.getElementById('holidays').textContent = holidayCount;
    
    // 计算周末天数
    const weekends = this.calculateWeekends(this.currentYear);
    document.getElementById('weekends').textContent = weekends;
  }

  /**
   * 计算年份中的周末天数
   */
  calculateWeekends(year) {
    let weekends = 0;
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year + 1, 0, 1);
    
    for (let date = new Date(startDate); date < endDate; date.setDate(date.getDate() + 1)) {
      if (date.getDay() === 0 || date.getDay() === 6) {
        weekends++;
      }
    }
    
    return weekends;
  }

  /**
   * 打开备忘录模态框
   */
  openMemoModal(date = null) {
    if (window.memoManager) {
      window.memoManager.openModal(date);
    }
  }

  /**
   * 打开月份详情页面
   */
  openMonthDetail(month) {
    console.log(`打开${month}月详情页面`);
    // 这里可以实现月份详情页面
  }
}

// 全局日历实例
window.calendar = null;
