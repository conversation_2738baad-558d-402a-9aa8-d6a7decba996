const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  let pathname = parsedUrl.pathname;
  
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // API路由处理
  if (pathname.startsWith('/api/')) {
    handleApiRequest(req, res, pathname);
    return;
  }
  
  // 静态文件处理
  if (pathname === '/') {
    pathname = '/index.html';
  }
  
  const filePath = path.join(__dirname, 'public', pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end('<h1>404 - 文件未找到</h1>');
      return;
    }
    
    // 获取文件扩展名
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'application/octet-stream';
    
    // 读取并返回文件
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/html' });
        res.end('<h1>500 - 服务器内部错误</h1>');
        return;
      }
      
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  });
});

// API请求处理
function handleApiRequest(req, res, pathname) {
  res.setHeader('Content-Type', 'application/json');
  
  // 模拟API响应
  if (pathname === '/api/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      message: '服务器运行正常'
    }));
    return;
  }
  
  if (pathname === '/api/info') {
    res.writeHead(200);
    res.end(JSON.stringify({
      name: '智能日历系统 - 简化版',
      version: '1.0.0',
      description: '基于Node.js的智能日历系统'
    }));
    return;
  }
  
  // 模拟当前用户信息
  if (pathname === '/api/auth/me' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      user: {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        fullName: '系统管理员'
      }
    }));
    return;
  }

  // 模拟用户认证
  if (pathname === '/api/auth/login' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    
    req.on('end', () => {
      try {
        const { username, password } = JSON.parse(body);
        
        // 简单的用户验证
        if (username === 'admin' && password === '123456') {
          res.writeHead(200);
          res.end(JSON.stringify({
            message: '登录成功',
            token: 'mock_jwt_token_' + Date.now(),
            user: {
              id: 1,
              username: 'admin',
              email: '<EMAIL>',
              fullName: '系统管理员'
            }
          }));
        } else {
          res.writeHead(401);
          res.end(JSON.stringify({
            error: '用户名或密码错误'
          }));
        }
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({
          error: '请求数据格式错误'
        }));
      }
    });
    return;
  }
  
  // 模拟备忘录API
  if (pathname === '/api/memos' && req.method === 'GET') {
    res.writeHead(200);
    res.end(JSON.stringify({
      memos: [
        {
          id: 1,
          title: '示例备忘录',
          content: '这是一个示例备忘录',
          memo_date: '2025-01-16',
          memo_time: '09:00:00',
          priority: 'medium',
          category: 'work',
          status: 'pending',
          reminder_minutes: 15,
          tags: '["示例", "测试"]'
        }
      ],
      pagination: {
        page: 1,
        limit: 100,
        total: 1,
        pages: 1
      }
    }));
    return;
  }

  // 创建备忘录
  if (pathname === '/api/memos' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const newMemo = {
          id: Date.now(),
          title: data.title,
          content: data.content || null,
          memo_date: data.memoDate,
          memo_time: data.memoTime || null,
          priority: data.priority || 'medium',
          category: data.category || 'other',
          status: data.status || 'pending',
          reminder_minutes: data.reminderMinutes || null,
          tags: data.tags ? JSON.stringify(data.tags) : null,
          created_at: new Date().toISOString()
        };

        console.log('✅ 备忘录已保存到模拟数据库:', newMemo);

        res.writeHead(201);
        res.end(JSON.stringify({
          message: '备忘录创建成功',
          memo: newMemo
        }));
      } catch (error) {
        console.error('❌ 创建备忘录错误:', error);
        res.writeHead(400);
        res.end(JSON.stringify({ error: '数据格式错误' }));
      }
    });
    return;
  }

  // 更新备忘录
  if (pathname.startsWith('/api/memos/') && req.method === 'PUT') {
    const memoId = pathname.split('/')[3];
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });

    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        const updatedMemo = {
          id: parseInt(memoId),
          title: data.title,
          content: data.content || null,
          memo_date: data.memoDate,
          memo_time: data.memoTime || null,
          priority: data.priority || 'medium',
          category: data.category || 'other',
          status: data.status || 'pending',
          reminder_minutes: data.reminderMinutes || null,
          tags: data.tags ? JSON.stringify(data.tags) : null,
          updated_at: new Date().toISOString()
        };

        console.log('✅ 备忘录已更新:', updatedMemo);

        res.writeHead(200);
        res.end(JSON.stringify({
          message: '备忘录更新成功',
          memo: updatedMemo
        }));
      } catch (error) {
        console.error('❌ 更新备忘录错误:', error);
        res.writeHead(400);
        res.end(JSON.stringify({ error: '数据格式错误' }));
      }
    });
    return;
  }

  // 删除备忘录
  if (pathname.startsWith('/api/memos/') && req.method === 'DELETE') {
    const memoId = pathname.split('/')[3];

    console.log('✅ 备忘录已删除:', memoId);

    res.writeHead(200);
    res.end(JSON.stringify({
      message: '备忘录删除成功'
    }));
    return;
  }
  
  // 模拟节假日API
  if (pathname.startsWith('/api/holidays/year/')) {
    const year = pathname.split('/').pop();
    res.writeHead(200);
    res.end(JSON.stringify({
      year: parseInt(year),
      holidays: {
        legal: [
          { name: '元旦', holiday_date: `${year}-01-01`, type: 'legal' },
          { name: '春节', holiday_date: `${year}-02-10`, type: 'legal' },
          { name: '清明节', holiday_date: `${year}-04-05`, type: 'legal' },
          { name: '劳动节', holiday_date: `${year}-05-01`, type: 'legal' },
          { name: '端午节', holiday_date: `${year}-06-02`, type: 'legal' },
          { name: '国庆节', holiday_date: `${year}-10-01`, type: 'legal' }
        ],
        traditional: [
          { name: '情人节', holiday_date: `${year}-02-14`, type: 'traditional' },
          { name: '妇女节', holiday_date: `${year}-03-08`, type: 'traditional' },
          { name: '儿童节', holiday_date: `${year}-06-01`, type: 'traditional' },
          { name: '教师节', holiday_date: `${year}-09-10`, type: 'traditional' },
          { name: '圣诞节', holiday_date: `${year}-12-25`, type: 'traditional' }
        ],
        solar_term: [
          { name: '立春', holiday_date: `${year}-02-04`, type: 'solar_term' },
          { name: '春分', holiday_date: `${year}-03-20`, type: 'solar_term' },
          { name: '立夏', holiday_date: `${year}-05-05`, type: 'solar_term' },
          { name: '夏至', holiday_date: `${year}-06-21`, type: 'solar_term' },
          { name: '立秋', holiday_date: `${year}-08-07`, type: 'solar_term' },
          { name: '秋分', holiday_date: `${year}-09-23`, type: 'solar_term' },
          { name: '立冬', holiday_date: `${year}-11-07`, type: 'solar_term' },
          { name: '冬至', holiday_date: `${year}-12-22`, type: 'solar_term' }
        ]
      },
      total: 19
    }));
    return;
  }
  
  // 默认404响应
  res.writeHead(404);
  res.end(JSON.stringify({
    error: 'API端点不存在'
  }));
}

// 启动服务器
server.listen(PORT, () => {
  console.log(`
🚀 智能日历系统服务器启动成功！

📍 服务器地址: http://localhost:${PORT}
📍 服务器地址: http://127.0.0.1:${PORT}
📍 API地址: http://localhost:${PORT}/api
📍 健康检查: http://localhost:${PORT}/api/health

🔧 环境: 开发模式
💾 数据: 模拟数据（内存存储）

按 Ctrl+C 停止服务器
  `);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
