const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');

const PORT = 3000;

// MySQL连接状态
let hasMySQL = false;
let db = null;

try {
  hasMySQL = true;
  
  // 创建数据库连接池
  db = mysql.createPool({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '521223',
    database: 'smart_calendar',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    charset: 'utf8mb4'
  });
  
  console.log('✅ MySQL模块已加载，将使用数据库存储');

  // 初始化数据库
  initDatabase();
} catch (error) {
  console.log('⚠️  MySQL模块未安装或配置错误，使用内存存储模式');
  console.log('💡 要使用MySQL数据库，请运行: npm install mysql2');
}

// 内存存储（仅用于备忘录缓存，用户数据完全使用MySQL）
let memoryMemos = new Map();
let memoIdCounter = Date.now();

// MIME类型映射
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon'
};

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  let pathname = parsedUrl.pathname;
  
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  // 处理Chrome DevTools检测请求
  if (pathname === '/.well-known/appspecific/com.chrome.devtools.json') {
    res.writeHead(404);
    res.end();
    return;
  }
  
  // 用户认证API路由
  if (pathname.startsWith('/api/auth/')) {
    handleAuthAPI(req, res, pathname, req.method);
    return;
  }

  // API路由处理
  if (pathname.startsWith('/api/')) {
    handleApiRequest(req, res, pathname);
    return;
  }
  
  // 静态文件处理
  if (pathname === '/') {
    pathname = '/index.html';
  }
  
  const filePath = path.join(__dirname, 'public', pathname);
  
  // 检查文件是否存在
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end('<h1>404 - 文件未找到</h1>');
      return;
    }
    
    // 获取文件扩展名
    const ext = path.extname(filePath);
    const contentType = mimeTypes[ext] || 'application/octet-stream';
    
    // 读取并返回文件
    fs.readFile(filePath, (err, data) => {
      if (err) {
        res.writeHead(500, { 'Content-Type': 'text/html' });
        res.end('<h1>500 - 服务器内部错误</h1>');
        return;
      }
      
      res.writeHead(200, { 'Content-Type': contentType });
      res.end(data);
    });
  });
});

// API请求处理
async function handleApiRequest(req, res, pathname) {
  res.setHeader('Content-Type', 'application/json');
  
  try {
    // 健康检查
    if (pathname === '/api/health') {
      res.writeHead(200);
      res.end(JSON.stringify({
        status: 'ok',
        timestamp: new Date().toISOString(),
        database: hasMySQL ? 'mysql' : 'memory',
        message: '服务器运行正常'
      }));
      return;
    }
    
    // 备忘录相关API
    if (pathname === '/api/memos' && req.method === 'GET') {
      const memos = await getMemos();
      res.writeHead(200);
      res.end(JSON.stringify({ memos }));
      return;
    }
    
    if (pathname === '/api/memos' && req.method === 'POST') {
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', async () => {
        try {
          const data = JSON.parse(body);
          const memo = await createMemo(data);
          
          console.log('✅ 备忘录已保存:', memo);
          
          res.writeHead(201);
          res.end(JSON.stringify({
            message: '备忘录创建成功',
            memo: memo
          }));
        } catch (error) {
          console.error('❌ 创建备忘录错误:', error);
          res.writeHead(400);
          res.end(JSON.stringify({ error: '创建备忘录失败: ' + error.message }));
        }
      });
      return;
    }
    
    // 更新备忘录
    if (pathname.startsWith('/api/memos/') && req.method === 'PUT') {
      const memoId = pathname.split('/')[3];
      let body = '';
      req.on('data', chunk => {
        body += chunk.toString();
      });
      
      req.on('end', async () => {
        try {
          const data = JSON.parse(body);
          const memo = await updateMemo(memoId, data);
          
          console.log('✅ 备忘录已更新:', memo);
          
          res.writeHead(200);
          res.end(JSON.stringify({
            message: '备忘录更新成功',
            memo: memo
          }));
        } catch (error) {
          console.error('❌ 更新备忘录错误:', error);
          res.writeHead(400);
          res.end(JSON.stringify({ error: '更新备忘录失败: ' + error.message }));
        }
      });
      return;
    }
    
    // 删除备忘录
    if (pathname.startsWith('/api/memos/') && req.method === 'DELETE') {
      const memoId = pathname.split('/')[3];
      
      try {
        await deleteMemo(memoId);
        
        console.log('✅ 备忘录已删除:', memoId);
        
        res.writeHead(200);
        res.end(JSON.stringify({
          message: '备忘录删除成功'
        }));
      } catch (error) {
        console.error('❌ 删除备忘录错误:', error);
        res.writeHead(400);
        res.end(JSON.stringify({ error: '删除备忘录失败: ' + error.message }));
      }
      return;
    }
    
    // 节假日API
    if (pathname.startsWith('/api/holidays/year/')) {
      const year = pathname.split('/').pop();
      res.writeHead(200);
      res.end(JSON.stringify({
        year: parseInt(year),
        holidays: {
          legal: [
            { name: '元旦', holiday_date: `${year}-01-01`, type: 'legal' },
            { name: '春节', holiday_date: `${year}-02-10`, type: 'legal' },
            { name: '清明节', holiday_date: `${year}-04-05`, type: 'legal' },
            { name: '劳动节', holiday_date: `${year}-05-01`, type: 'legal' },
            { name: '端午节', holiday_date: `${year}-06-02`, type: 'legal' },
            { name: '国庆节', holiday_date: `${year}-10-01`, type: 'legal' }
          ],
          traditional: [
            { name: '情人节', holiday_date: `${year}-02-14`, type: 'traditional' },
            { name: '妇女节', holiday_date: `${year}-03-08`, type: 'traditional' },
            { name: '儿童节', holiday_date: `${year}-06-01`, type: 'traditional' },
            { name: '教师节', holiday_date: `${year}-09-10`, type: 'traditional' },
            { name: '圣诞节', holiday_date: `${year}-12-25`, type: 'traditional' }
          ],
          solar_term: [
            { name: '立春', holiday_date: `${year}-02-04`, type: 'solar_term' },
            { name: '春分', holiday_date: `${year}-03-20`, type: 'solar_term' },
            { name: '立夏', holiday_date: `${year}-05-05`, type: 'solar_term' },
            { name: '夏至', holiday_date: `${year}-06-21`, type: 'solar_term' },
            { name: '立秋', holiday_date: `${year}-08-07`, type: 'solar_term' },
            { name: '秋分', holiday_date: `${year}-09-23`, type: 'solar_term' },
            { name: '立冬', holiday_date: `${year}-11-07`, type: 'solar_term' },
            { name: '冬至', holiday_date: `${year}-12-22`, type: 'solar_term' }
          ]
        }
      }));
      return;
    }
    
    // 默认404响应
    res.writeHead(404);
    res.end(JSON.stringify({
      error: 'API端点不存在'
    }));
    
  } catch (error) {
    console.error('API处理错误:', error);
    res.writeHead(500);
    res.end(JSON.stringify({
      error: '服务器内部错误'
    }));
  }
}

// 数据库操作函数
async function getMemos() {
  if (hasMySQL && db) {
    try {
      const [rows] = await db.execute(
        'SELECT * FROM memos WHERE user_id = 1 ORDER BY memo_date DESC, created_at DESC'
      );
      return rows;
    } catch (error) {
      console.error('数据库查询失败，使用内存存储:', error);
      return Array.from(memoryMemos.values());
    }
  } else {
    return Array.from(memoryMemos.values());
  }
}

async function createMemo(data) {
  const memo = {
    id: Date.now(),
    user_id: 1,
    title: data.title,
    content: data.content || null,
    memo_date: data.memoDate,
    memo_time: data.memoTime || null,
    priority: data.priority || 'medium',
    category: data.category || 'other',
    status: data.status || 'pending',
    reminder_minutes: data.reminderMinutes || null,
    tags: data.tags ? JSON.stringify(data.tags) : null,
    created_at: new Date().toISOString()
  };
  
  if (hasMySQL && db) {
    try {
      const [result] = await db.execute(
        `INSERT INTO memos (user_id, title, content, memo_date, memo_time, priority, category, status, reminder_minutes, tags) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [memo.user_id, memo.title, memo.content, memo.memo_date, memo.memo_time, 
         memo.priority, memo.category, memo.status, memo.reminder_minutes, memo.tags]
      );
      memo.id = result.insertId;
      console.log('📊 备忘录已写入MySQL数据库');
    } catch (error) {
      console.error('数据库写入失败，使用内存存储:', error);
      memoryMemos.set(memo.id.toString(), memo);
      console.log('💾 备忘录已保存到内存');
    }
  } else {
    memoryMemos.set(memo.id.toString(), memo);
    console.log('💾 备忘录已保存到内存');
  }
  
  return memo;
}

async function updateMemo(id, data) {
  const memo = {
    id: parseInt(id),
    title: data.title,
    content: data.content || null,
    memo_date: data.memoDate,
    memo_time: data.memoTime || null,
    priority: data.priority || 'medium',
    category: data.category || 'other',
    status: data.status || 'pending',
    reminder_minutes: data.reminderMinutes || null,
    tags: data.tags ? JSON.stringify(data.tags) : null,
    updated_at: new Date().toISOString()
  };
  
  if (hasMySQL && db) {
    try {
      await db.execute(
        `UPDATE memos SET title = ?, content = ?, memo_date = ?, memo_time = ?, 
         priority = ?, category = ?, status = ?, reminder_minutes = ?, tags = ? 
         WHERE id = ? AND user_id = 1`,
        [memo.title, memo.content, memo.memo_date, memo.memo_time, 
         memo.priority, memo.category, memo.status, memo.reminder_minutes, memo.tags, memo.id]
      );
      console.log('📊 备忘录已在MySQL数据库中更新');
    } catch (error) {
      console.error('数据库更新失败，使用内存存储:', error);
      memoryMemos.set(id, memo);
    }
  } else {
    memoryMemos.set(id, memo);
  }
  
  return memo;
}

async function deleteMemo(id) {
  if (hasMySQL && db) {
    try {
      await db.execute('DELETE FROM memos WHERE id = ? AND user_id = 1', [id]);
      console.log('📊 备忘录已从MySQL数据库中删除');
    } catch (error) {
      console.error('数据库删除失败，使用内存存储:', error);
      memoryMemos.delete(id);
    }
  } else {
    memoryMemos.delete(id);
  }
}

// 用户认证API处理函数
async function handleAuthAPI(req, res, pathname, method) {
  const segments = pathname.split('/');
  const endpoint = segments[3]; // /api/auth/{endpoint}

  console.log('🔗 Auth API 调用:', { pathname, method, endpoint });

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    switch (endpoint) {
      case 'register':
        if (method === 'POST') {
          console.log('📝 调用注册处理函数');
          await handleRegister(req, res);
        } else {
          res.writeHead(405);
          res.end(JSON.stringify({ error: '方法不允许' }));
        }
        break;

      case 'login':
        if (method === 'POST') {
          console.log('🔐 调用登录处理函数');
          await handleLogin(req, res);
        } else {
          res.writeHead(405);
          res.end(JSON.stringify({ error: '方法不允许' }));
        }
        break;

      case 'me':
        if (method === 'GET') {
          await handleGetCurrentUser(req, res);
        } else {
          res.writeHead(405);
          res.end(JSON.stringify({ error: '方法不允许' }));
        }
        break;

      case 'profile':
        if (method === 'PUT') {
          await handleUpdateProfile(req, res);
        } else {
          res.writeHead(405);
          res.end(JSON.stringify({ error: '方法不允许' }));
        }
        break;

      case 'change-password':
        if (method === 'PUT') {
          await handleChangePassword(req, res);
        } else {
          res.writeHead(405);
          res.end(JSON.stringify({ error: '方法不允许' }));
        }
        break;

      default:
        res.writeHead(404);
        res.end(JSON.stringify({ error: '接口不存在' }));
    }
  } catch (error) {
    console.error('❌ 认证API错误:', error);
    res.writeHead(500);
    res.end(JSON.stringify({ error: '服务器内部错误' }));
  }
}

// 用户注册处理
async function handleRegister(req, res) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', async () => {
    try {
      const { username, email, fullName, password } = JSON.parse(body);

      console.log('📝 注册请求:', { username, email, fullName, hasPassword: !!password });

      // 验证输入
      if (!username || !email || !fullName || !password) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: '所有字段都是必填的' }));
        return;
      }

      if (!db) {
        console.error('❌ 数据库连接不可用');
        res.writeHead(500);
        res.end(JSON.stringify({ error: '数据库服务不可用' }));
        return;
      }

      try {
        console.log('🔍 检查用户名和邮箱是否已存在...');

        // 检查用户是否已存在
        const [existingUsers] = await db.execute(
          'SELECT id, username, email FROM users WHERE username = ? OR email = ?',
          [username, email]
        );

        if (existingUsers.length > 0) {
          const existing = existingUsers[0];
          const conflict = existing.username === username ? '用户名' : '邮箱';
          console.log('❌ 用户已存在:', { conflict, username, email });
          res.writeHead(409);
          res.end(JSON.stringify({ error: `${conflict}已存在` }));
          return;
        }

        console.log('🔐 开始加密密码...');
        // 加密密码
        const hashedPassword = await bcrypt.hash(password, 10);
        console.log('✅ 密码加密完成');

        console.log('📝 创建新用户记录...');
        // 创建新用户
        const [result] = await db.execute(`
          INSERT INTO users (username, email, password_hash, full_name, is_active, created_at)
          VALUES (?, ?, ?, ?, ?, NOW())
        `, [username, email, hashedPassword, fullName, true]);

        console.log('🎉 用户注册成功 (MySQL):', {
          username,
          email,
          fullName,
          userId: result.insertId
        });

        res.writeHead(201);
        res.end(JSON.stringify({
          message: '注册成功！请使用您的用户名和密码登录。',
          user: {
            id: result.insertId,
            username,
            email,
            full_name: fullName
          }
        }));

      } catch (dbError) {
        console.error('❌ MySQL数据库注册错误:', {
          message: dbError.message,
          code: dbError.code,
          errno: dbError.errno
        });

        // 处理特定的数据库错误
        if (dbError.code === 'ER_DUP_ENTRY') {
          res.writeHead(409);
          res.end(JSON.stringify({ error: '用户名或邮箱已存在' }));
        } else {
          res.writeHead(500);
          res.end(JSON.stringify({
            error: '注册失败，请稍后重试',
            details: process.env.NODE_ENV === 'development' ? dbError.message : undefined
          }));
        }
      }
    } catch (error) {
      console.error('❌ 注册错误:', error);
      res.writeHead(500);
      res.end(JSON.stringify({ error: '服务器内部错误' }));
    }
  });
}

// 用户登录处理 - 纯MySQL版本
async function handleLogin(req, res) {
  console.log('🚀 MySQL登录处理开始');

  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', async () => {
    try {
      console.log('📥 接收到的登录数据:', body);
      const { username, password, rememberMe } = JSON.parse(body);
      console.log('🔍 登录请求:', {
        username: username || '(空)',
        hasPassword: !!password,
        rememberMe: !!rememberMe
      });

      // 严格的输入验证
      if (!username || !password) {
        console.log('❌ 输入验证失败: 用户名或密码为空');
        res.writeHead(400);
        res.end(JSON.stringify({ error: '用户名和密码不能为空' }));
        return;
      }

      if (!db) {
        console.error('❌ 数据库连接不可用');
        res.writeHead(500);
        res.end(JSON.stringify({ error: '数据库服务不可用' }));
        return;
      }

      try {
        console.log('🔍 正在MySQL数据库中查找用户:', username);

        // 查找用户 - 支持用户名或邮箱登录
        const [users] = await db.execute(`
          SELECT
            id, username, email, password_hash, full_name,
            is_active, created_at, last_login
          FROM users
          WHERE (username = ? OR email = ?) AND is_active = TRUE
        `, [username, username]);

        console.log('🔍 数据库查询结果:', {
          found: users.length > 0,
          userCount: users.length
        });

        if (users.length === 0) {
          console.log('❌ 用户不存在或已禁用:', username);
          res.writeHead(401);
          res.end(JSON.stringify({ error: '用户名或密码错误' }));
          return;
        }

        const user = users[0];
        console.log('✅ 找到用户:', {
          id: user.id,
          username: user.username,
          email: user.email,
          isActive: user.is_active
        });

        // 使用bcrypt验证密码
        console.log('🔐 开始密码验证...');
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        console.log('🔍 密码验证结果:', {
          username: user.username,
          isValid: isPasswordValid
        });

        if (!isPasswordValid) {
          console.log('❌ 密码验证失败:', { username: user.username });
          res.writeHead(401);
          res.end(JSON.stringify({ error: '用户名或密码错误' }));
          return;
        }

        // 更新最后登录时间
        console.log('📝 更新最后登录时间...');
        await db.execute('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);

        // 生成会话token
        const token = `mysql_token_${user.id}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

        // 可选：将token保存到数据库的会话表
        const expiresAt = new Date();
        expiresAt.setHours(expiresAt.getHours() + (rememberMe ? 24 * 7 : 24)); // 记住我：7天，否则1天

        try {
          await db.execute(`
            INSERT INTO user_sessions (user_id, token, expires_at)
            VALUES (?, ?, ?)
          `, [user.id, token, expiresAt]);
          console.log('✅ 会话token已保存到数据库');
        } catch (sessionError) {
          console.log('⚠️ 会话保存失败，但登录继续:', sessionError.message);
        }

        console.log('🎉 用户登录成功 (MySQL):', {
          userId: user.id,
          username: user.username,
          email: user.email,
          tokenGenerated: !!token
        });

        res.writeHead(200);
        res.end(JSON.stringify({
          message: '登录成功',
          token,
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            full_name: user.full_name,
            last_login: user.last_login
          }
        }));

      } catch (dbError) {
        console.error('❌ MySQL数据库操作错误:', {
          message: dbError.message,
          code: dbError.code,
          errno: dbError.errno
        });
        res.writeHead(500);
        res.end(JSON.stringify({
          error: '数据库操作失败，请稍后重试',
          details: process.env.NODE_ENV === 'development' ? dbError.message : undefined
        }));
      }

    } catch (parseError) {
      console.error('❌ 请求数据解析错误:', parseError);
      res.writeHead(400);
      res.end(JSON.stringify({ error: '请求数据格式错误' }));
    }
  });
}

// 获取当前用户信息
async function handleGetCurrentUser(req, res) {
  try {
    // 简化的token验证
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    let userId = 1; // 默认用户
    if (token && token.startsWith('token_')) {
      const parts = token.split('_');
      userId = parseInt(parts[1]) || 1;
    }

    const user = memoryUsers.get(userId);
    if (!user) {
      res.writeHead(404);
      res.end(JSON.stringify({ error: '用户不存在' }));
      return;
    }

    res.writeHead(200);
    res.end(JSON.stringify({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        created_at: user.created_at,
        last_login: user.last_login
      }
    }));

  } catch (error) {
    console.error('❌ 获取用户信息失败:', error);
    res.writeHead(500);
    res.end(JSON.stringify({ error: '获取用户信息失败' }));
  }
}

// 更新用户资料
async function handleUpdateProfile(req, res) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', async () => {
    try {
      const { email, fullName } = JSON.parse(body);

      // 简化的token验证
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];

      let userId = 1; // 默认用户
      if (token && token.startsWith('token_')) {
        const parts = token.split('_');
        userId = parseInt(parts[1]) || 1;
      }

      const user = memoryUsers.get(userId);
      if (!user) {
        res.writeHead(404);
        res.end(JSON.stringify({ error: '用户不存在' }));
        return;
      }

      // 更新用户信息
      if (email) user.email = email;
      if (fullName) user.full_name = fullName;

      console.log('✅ 用户资料更新成功:', { userId, email, fullName });

      res.writeHead(200);
      res.end(JSON.stringify({ message: '用户信息更新成功' }));

    } catch (error) {
      console.error('❌ 更新用户资料失败:', error);
      res.writeHead(400);
      res.end(JSON.stringify({ error: '数据格式错误' }));
    }
  });
}

// 修改密码
async function handleChangePassword(req, res) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });

  req.on('end', async () => {
    try {
      const { currentPassword, newPassword } = JSON.parse(body);

      // 验证输入
      if (!currentPassword || !newPassword) {
        res.writeHead(400);
        res.end(JSON.stringify({ error: '当前密码和新密码不能为空' }));
        return;
      }

      // 简化的密码验证
      if (currentPassword !== '123456') {
        res.writeHead(401);
        res.end(JSON.stringify({ error: '当前密码错误' }));
        return;
      }

      console.log('✅ 密码修改成功（模拟）');

      res.writeHead(200);
      res.end(JSON.stringify({ message: '密码修改成功' }));

    } catch (error) {
      console.error('❌ 修改密码失败:', error);
      res.writeHead(400);
      res.end(JSON.stringify({ error: '数据格式错误' }));
    }
  });
}

// 启动服务器
server.listen(PORT, () => {
  console.log(`
🚀 智能日历系统混合服务器启动成功！

📍 服务器地址: http://localhost:${PORT}
📍 服务器地址: http://127.0.0.1:${PORT}
📍 API地址: http://localhost:${PORT}/api
📍 健康检查: http://localhost:${PORT}/api/health

🔧 环境: 开发模式
💾 数据存储: ${hasMySQL ? 'MySQL数据库' : '内存存储'}
${hasMySQL ? '📊 数据将持久化保存到MySQL数据库' : '⚠️  数据仅保存在内存中，重启后丢失'}

按 Ctrl+C 停止服务器
  `);
});

// 数据库初始化函数
async function initDatabase() {
  try {
    console.log('🔗 正在连接MySQL数据库...');

    // 首先连接到MySQL服务器（不指定数据库）
    const tempConnection = await mysql.createConnection({
      host: '127.0.0.1',
      user: 'root',
      password: '521223',
      charset: 'utf8mb4'
    });

    // 创建数据库（如果不存在）
    await tempConnection.execute(`CREATE DATABASE IF NOT EXISTS \`smart_calendar\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ 数据库 smart_calendar 已准备就绪');

    await tempConnection.end();

    // 测试连接池
    const connection = await db.getConnection();
    console.log('✅ MySQL数据库连接成功');
    connection.release();

    // 初始化数据表
    await initTables();

    hasMySQL = true;
  } catch (error) {
    console.error('❌ MySQL数据库连接失败:', error.message);
    hasMySQL = false;
  }
}

// 初始化数据表
async function initTables() {
  try {
    console.log('📋 正在初始化数据表...');

    // 用户表
    await db.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_username (username),
        INDEX idx_email (email)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 备忘录表
    await db.execute(`
      CREATE TABLE IF NOT EXISTS memos (
        id BIGINT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(200) NOT NULL,
        content TEXT,
        memo_date DATE NOT NULL,
        memo_time TIME NULL,
        priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
        category VARCHAR(50) DEFAULT 'general',
        status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
        reminder_minutes INT NULL,
        tags JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_date (user_id, memo_date),
        INDEX idx_date (memo_date),
        INDEX idx_status (status)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // 用户会话表
    await db.execute(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) UNIQUE NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_token (token),
        INDEX idx_user_id (user_id),
        INDEX idx_expires (expires_at)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    console.log('✅ 数据表初始化完成');

    // 创建默认管理员用户
    await createDefaultUser();

  } catch (error) {
    console.error('❌ 数据表初始化失败:', error.message);
    throw error;
  }
}

// 创建默认管理员用户
async function createDefaultUser() {
  try {
    // 检查是否已存在管理员用户
    const [rows] = await db.execute('SELECT id FROM users WHERE username = ?', ['admin']);

    if (rows.length === 0) {
      // 创建默认管理员用户
      const hashedPassword = await bcrypt.hash('123456', 10);

      await db.execute(`
        INSERT INTO users (username, email, password_hash, full_name, is_active)
        VALUES (?, ?, ?, ?, ?)
      `, ['admin', '<EMAIL>', hashedPassword, '系统管理员', true]);

      console.log('👤 默认管理员用户已创建: admin / 123456');
    } else {
      console.log('👤 默认管理员用户已存在');
    }
  } catch (error) {
    console.error('❌ 创建默认用户失败:', error.message);
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n收到SIGINT信号，正在关闭服务器...');
  server.close(() => {
    if (db) {
      db.end();
    }
    console.log('服务器已关闭');
    process.exit(0);
  });
});
