/**
 * 用户认证模块
 */
class AuthManager {
  constructor() {
    this.currentUser = null;
    this.token = localStorage.getItem('auth_token');
    this.loginModal = null;
    
    this.init();
  }

  /**
   * 初始化认证管理器
   */
  init() {
    this.loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    this.bindEvents();
    this.checkAuthStatus();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 登录表单提交
    document.getElementById('loginForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleLogin();
    });

    // 注册表单提交
    document.getElementById('registerForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleRegister();
    });

    // 切换到注册表单
    document.getElementById('showRegister').addEventListener('click', (e) => {
      e.preventDefault();
      this.showRegisterForm();
    });

    // 切换到登录表单
    document.getElementById('showLogin').addEventListener('click', (e) => {
      e.preventDefault();
      this.showLoginForm();
    });

    // 退出登录
    document.getElementById('logoutBtn').addEventListener('click', (e) => {
      e.preventDefault();
      this.logout();
    });
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus() {
    if (this.token) {
      try {
        // 验证token是否有效
        const response = await window.api.getCurrentUser();
        this.currentUser = response.user;
        this.showApp();
      } catch (error) {
        console.error('Token验证失败:', error);
        // 即使token验证失败，也显示应用（使用游客模式）
        this.showApp();
      }
    } else {
      // 直接显示应用，不强制登录
      this.showApp();
    }
  }

  /**
   * 处理登录
   */
  async handleLogin() {
    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value;

    if (!username || !password) {
      this.showError('请输入用户名和密码');
      return;
    }

    try {
      const response = await window.api.login(username, password);
      
      this.token = response.token;
      this.currentUser = response.user;
      
      // 保存token
      window.api.setToken(this.token);
      
      // 显示成功消息
      this.showSuccess('登录成功！');
      
      // 隐藏登录模态框，显示应用
      this.loginModal.hide();
      this.showApp();
      
    } catch (error) {
      console.error('登录失败:', error);
      this.showError(error.message || '登录失败，请重试');
    }
  }

  /**
   * 处理注册
   */
  async handleRegister() {
    const username = document.getElementById('registerUsername').value.trim();
    const email = document.getElementById('registerEmail').value.trim();
    const password = document.getElementById('registerPassword').value;
    const fullName = document.getElementById('registerFullName').value.trim();

    if (!username || !email || !password) {
      this.showError('请填写必填字段');
      return;
    }

    try {
      const response = await window.api.register(username, email, password, fullName);
      
      this.token = response.token;
      this.currentUser = response.user;
      
      // 保存token
      window.api.setToken(this.token);
      
      // 显示成功消息
      this.showSuccess('注册成功！');
      
      // 隐藏登录模态框，显示应用
      this.loginModal.hide();
      this.showApp();
      
    } catch (error) {
      console.error('注册失败:', error);
      this.showError(error.message || '注册失败，请重试');
    }
  }

  /**
   * 退出登录
   */
  async logout() {
    try {
      if (this.token) {
        await window.api.logout();
      }
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      // 清除本地数据
      this.token = null;
      this.currentUser = null;
      window.api.setToken(null);
      
      // 显示登录模态框
      this.showLoginModal();
    }
  }

  /**
   * 显示登录模态框
   */
  showLoginModal() {
    document.getElementById('app').style.display = 'none';
    document.getElementById('loadingScreen').style.display = 'none';
    this.loginModal.show();
  }

  /**
   * 显示应用
   */
  showApp() {
    document.getElementById('loadingScreen').style.display = 'none';
    document.getElementById('app').style.display = 'block';

    // 更新用户显示名称
    if (this.currentUser) {
      document.getElementById('userDisplayName').textContent =
        this.currentUser.fullName || this.currentUser.username;
    } else {
      // 游客模式
      document.getElementById('userDisplayName').textContent = '游客';
    }

    // 初始化应用
    if (window.app && window.app.initializeApp) {
      window.app.initializeApp();
    }
  }

  /**
   * 显示注册表单
   */
  showRegisterForm() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'block';
    document.getElementById('authModalTitle').textContent = '用户注册';
  }

  /**
   * 显示登录表单
   */
  showLoginForm() {
    document.getElementById('registerForm').style.display = 'none';
    document.getElementById('loginForm').style.display = 'block';
    document.getElementById('authModalTitle').textContent = '用户登录';
  }

  /**
   * 显示错误消息
   */
  showError(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      <i class="bi bi-exclamation-triangle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 5000);
  }

  /**
   * 显示成功消息
   */
  showSuccess(message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      <i class="bi bi-check-circle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 3000);
  }

  /**
   * 获取当前用户
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated() {
    return !!this.token && !!this.currentUser;
  }
}

// 全局认证管理器实例
window.auth = null;
