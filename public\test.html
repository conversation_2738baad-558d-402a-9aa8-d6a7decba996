<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日历系统测试页面</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
  
  <style>
    body {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      font-family: 'Microsoft YaHei', 'Segoe UI', 'Roboto', sans-serif;
    }
    
    .month-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 15px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      cursor: pointer;
      height: 350px;
    }
    
    .month-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    
    .month-header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      text-align: center;
      padding: 15px;
      border-radius: 10px;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .days-grid {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 2px;
    }
    
    .weekday-cell {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      font-weight: 600;
      font-size: 0.85rem;
      padding: 8px 4px;
      text-align: center;
      border-radius: 6px;
    }
    
    .day-cell {
      padding: 4px 2px;
      font-size: 0.75rem;
      text-align: center;
      min-height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .day-cell:hover {
      background: rgba(102, 126, 234, 0.1);
      transform: scale(1.05);
    }
    
    .day-cell.today {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      font-weight: bold;
    }
    
    .day-cell.weekend {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
      color: #1976d2;
    }
  </style>
</head>
<body>
  <div class="container-fluid py-4">
    <div class="text-center mb-4">
      <h1 class="text-white mb-3">
        <i class="bi bi-calendar-event me-2"></i>
        智能日历系统测试
      </h1>
      <div class="text-white-50">
        <p>测试3×4布局年历显示</p>
      </div>
    </div>
    
    <!-- 年份显示 -->
    <div class="row justify-content-center mb-4">
      <div class="col-auto">
        <div class="bg-white bg-opacity-90 rounded-3 p-3 text-center">
          <h2 class="mb-0" id="currentYear">2025</h2>
          <small class="text-muted">乙巳蛇年</small>
        </div>
      </div>
    </div>
    
    <!-- 3×4日历网格 -->
    <div class="row g-4" id="calendarGrid">
      <!-- 月份卡片将通过JavaScript生成 -->
    </div>
    
    <!-- 测试按钮 -->
    <div class="text-center mt-4">
      <button class="btn btn-light me-2" onclick="generateTestCalendar()">
        <i class="bi bi-arrow-clockwise me-1"></i>生成测试日历
      </button>
      <button class="btn btn-outline-light me-2" onclick="testAPI()">
        <i class="bi bi-cloud-check me-1"></i>测试API
      </button>
      <button class="btn btn-outline-light" onclick="showDebugInfo()">
        <i class="bi bi-bug me-1"></i>调试信息
      </button>
    </div>
    
    <!-- 调试信息 -->
    <div class="mt-4">
      <div class="bg-dark text-light p-3 rounded" id="debugInfo" style="display: none;">
        <h5>调试信息</h5>
        <pre id="debugContent"></pre>
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  
  <script>
    // 生成测试日历
    function generateTestCalendar() {
      const grid = document.getElementById('calendarGrid');
      const currentYear = 2025;
      const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      
      let html = '';
      
      for (let month = 1; month <= 12; month++) {
        const monthData = generateMonthData(currentYear, month);
        
        html += `
          <div class="col-lg-4 col-md-6">
            <div class="month-card" onclick="alert('点击了${currentYear}年${months[month-1]}')">
              <div class="month-header">
                ${currentYear}年${months[month-1]}
              </div>
              
              <div class="days-grid mb-2">
                ${weekdays.map(day => `<div class="weekday-cell">${day}</div>`).join('')}
              </div>
              
              <div class="days-grid">
                ${monthData.map(day => {
                  const classes = ['day-cell'];
                  if (!day.isCurrentMonth) classes.push('text-muted');
                  if (day.isToday) classes.push('today');
                  if (day.isWeekend && day.isCurrentMonth) classes.push('weekend');
                  
                  return `<div class="${classes.join(' ')}" onclick="event.stopPropagation(); alert('点击了${day.date}')">${day.day}</div>`;
                }).join('')}
              </div>
            </div>
          </div>
        `;
      }
      
      grid.innerHTML = html;
      console.log('测试日历生成完成');
    }
    
    // 生成月份数据
    function generateMonthData(year, month) {
      const firstDay = new Date(year, month - 1, 1);
      const lastDay = new Date(year, month, 0);
      const daysInMonth = lastDay.getDate();
      const startWeekday = firstDay.getDay();
      const today = new Date();
      
      const days = [];
      
      // 上个月的日期
      const prevMonth = month === 1 ? 12 : month - 1;
      const prevYear = month === 1 ? year - 1 : year;
      const prevMonthDays = new Date(prevYear, prevMonth, 0).getDate();
      
      for (let i = startWeekday - 1; i >= 0; i--) {
        const day = prevMonthDays - i;
        days.push({
          day: day,
          isCurrentMonth: false,
          isToday: false,
          isWeekend: false,
          date: `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        });
      }
      
      // 当前月的日期
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        const isToday = date.toDateString() === today.toDateString();
        const isWeekend = date.getDay() === 0 || date.getDay() === 6;
        
        days.push({
          day: day,
          isCurrentMonth: true,
          isToday: isToday,
          isWeekend: isWeekend,
          date: dateStr
        });
      }
      
      // 下个月的日期
      const nextMonth = month === 12 ? 1 : month + 1;
      const nextYear = month === 12 ? year + 1 : year;
      const totalCells = 42; // 6行 × 7列
      const remainingCells = totalCells - days.length;
      
      for (let day = 1; day <= remainingCells && day <= 14; day++) {
        days.push({
          day: day,
          isCurrentMonth: false,
          isToday: false,
          isWeekend: false,
          date: `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`
        });
      }
      
      return days;
    }
    
    // 测试API
    async function testAPI() {
      try {
        console.log('开始测试API...');
        
        // 测试健康检查
        const healthResponse = await fetch('/api/health');
        const healthData = await healthResponse.json();
        console.log('健康检查:', healthData);
        
        // 测试节假日API
        const holidayResponse = await fetch('/api/holidays/year/2025');
        const holidayData = await holidayResponse.json();
        console.log('节假日数据:', holidayData);
        
        // 测试备忘录API
        const memoResponse = await fetch('/api/memos');
        const memoData = await memoResponse.json();
        console.log('备忘录数据:', memoData);
        
        alert('API测试完成，请查看控制台输出');
        
      } catch (error) {
        console.error('API测试失败:', error);
        alert('API测试失败: ' + error.message);
      }
    }
    
    // 显示调试信息
    function showDebugInfo() {
      const debugInfo = document.getElementById('debugInfo');
      const debugContent = document.getElementById('debugContent');
      
      const info = {
        '当前时间': new Date().toLocaleString(),
        '页面URL': window.location.href,
        '用户代理': navigator.userAgent,
        '屏幕尺寸': `${window.innerWidth} x ${window.innerHeight}`,
        'Bootstrap版本': typeof bootstrap !== 'undefined' ? '已加载' : '未加载',
        '控制台错误': '请查看浏览器控制台'
      };
      
      debugContent.textContent = JSON.stringify(info, null, 2);
      debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
    }
    
    // 页面加载完成后自动生成日历
    document.addEventListener('DOMContentLoaded', function() {
      console.log('页面加载完成，生成测试日历...');
      generateTestCalendar();
    });
  </script>
</body>
</html>
