<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能日历系统 - 登录</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 48px;
            width: 100%;
            max-width: 420px;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .logo-icon i {
            font-size: 28px;
            color: white;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .logo p {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            color: #1f2937;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
        }

        .input-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 18px;
            pointer-events: none;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #667eea;
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
        }

        .checkbox:checked {
            background: #667eea;
            border-color: #667eea;
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .checkbox-label {
            font-size: 14px;
            color: #6b7280;
            cursor: pointer;
        }

        .forgot-link {
            font-size: 14px;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-link:hover {
            color: #5a67d8;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .divider {
            text-align: center;
            margin: 32px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e7eb;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 16px;
            color: #9ca3af;
            font-size: 14px;
        }

        .register-link {
            text-align: center;
            margin-top: 24px;
        }

        .register-link p {
            color: #6b7280;
            font-size: 14px;
        }

        .register-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .register-link a:hover {
            color: #5a67d8;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        @media (max-width: 480px) {
            .login-container {
                padding: 32px 24px;
                margin: 16px;
            }

            .logo h1 {
                font-size: 24px;
            }

            .form-input {
                padding: 14px 16px;
                font-size: 16px;
            }
        }

        .demo-info {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .demo-info h4 {
            color: #1e293b;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .demo-info p {
            color: #64748b;
            font-size: 13px;
            margin-bottom: 8px;
        }

        .demo-credentials {
            background: #ffffff;
            border-radius: 6px;
            padding: 12px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <div class="logo-icon">
                <i class="bi bi-calendar-check"></i>
            </div>
            <h1>智能日历</h1>
            <p>欢迎回来，请登录您的账户</p>
        </div>

        <!-- 演示账户信息 -->
        <div class="demo-info">
            <h4><i class="bi bi-info-circle"></i> 演示账户</h4>
            <p>您可以使用以下账户进行测试：</p>
            <div class="demo-credentials">
                用户名：admin<br>
                密码：123456
            </div>
        </div>

        <!-- 状态提示 -->
        <div id="alertContainer"></div>

        <!-- 登录表单 -->
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">
                    <i class="bi bi-person"></i> 用户名或邮箱
                </label>
                <div style="position: relative;">
                    <input type="text" id="username" class="form-input" placeholder="请输入用户名或邮箱" required>
                    <i class="bi bi-person input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">
                    <i class="bi bi-lock"></i> 密码
                </label>
                <div style="position: relative;">
                    <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                    <i class="bi bi-eye password-toggle" id="passwordToggle"></i>
                </div>
            </div>

            <div class="form-options">
                <div class="checkbox-group">
                    <input type="checkbox" id="rememberMe" class="checkbox">
                    <label for="rememberMe" class="checkbox-label">记住我</label>
                </div>
                <a href="#" class="forgot-link">忘记密码？</a>
            </div>

            <button type="submit" class="login-btn" id="loginBtn">
                <span class="btn-text">登录</span>
                <div class="btn-loading">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="divider">
            <span>或者</span>
        </div>

        <div class="register-link">
            <p>还没有账户？ <a href="new-register.html" id="registerLink">立即注册</a></p>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        const passwordInput = document.getElementById('password');
        const passwordToggle = document.getElementById('passwordToggle');

        passwordToggle.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordToggle.className = 'bi bi-eye-slash password-toggle';
            } else {
                passwordInput.type = 'password';
                passwordToggle.className = 'bi bi-eye password-toggle';
            }
        });

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    ${message}
                </div>
            `;

            // 3秒后自动隐藏
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 3000);
        }

        // 设置按钮加载状态
        function setLoading(loading) {
            const loginBtn = document.getElementById('loginBtn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');

            if (loading) {
                loginBtn.disabled = true;
                btnText.style.opacity = '0';
                btnLoading.style.display = 'block';
            } else {
                loginBtn.disabled = false;
                btnText.style.opacity = '1';
                btnLoading.style.display = 'none';
            }
        }

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            if (!username || !password) {
                showAlert('请填写用户名和密码', 'error');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password, rememberMe })
                });

                const data = await response.json();

                if (response.ok) {
                    showAlert('登录成功！正在跳转...', 'success');
                    
                    // 保存token
                    localStorage.setItem('authToken', data.token);
                    
                    // 延迟跳转到主页面
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1500);
                } else {
                    showAlert(data.error || '登录失败', 'error');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                setLoading(false);
            }
        });

        // 注册链接点击（已改为直接跳转，无需阻止默认行为）

        // 忘记密码链接点击
        document.querySelector('.forgot-link').addEventListener('click', function(e) {
            e.preventDefault();
            showAlert('密码重置功能开发中...', 'info');
        });

        // 页面加载时检查是否已登录
        window.addEventListener('load', function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                // 验证token
                fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        showAlert('您已登录，正在跳转...', 'info');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    }
                })
                .catch(error => {
                    console.error('验证token失败:', error);
                    localStorage.removeItem('authToken');
                });
            }
        });

        // 演示账户快速填充
        document.querySelector('.demo-credentials').addEventListener('click', function() {
            document.getElementById('username').value = 'admin';
            document.getElementById('password').value = '123456';
            showAlert('演示账户信息已填充', 'info');
        });
    </script>
</body>
</html>
