const jwt = require('jsonwebtoken');
const db = require('../config/database');

/**
 * JWT认证中间件 - 修改为支持默认用户
 */
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    // 如果没有token，使用默认用户ID 1（admin用户）
    req.user = { userId: 1, username: 'admin' };
    return next();
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      // 如果token无效，也使用默认用户
      req.user = { userId: 1, username: 'admin' };
    } else {
      req.user = user;
    }
    next();
  });
}

/**
 * 可选认证中间件 - 如果有token则验证，没有则继续
 */
function optionalAuth(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    req.user = null;
    return next();
  }
  
  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      req.user = null;
    } else {
      req.user = user;
    }
    next();
  });
}

/**
 * 管理员权限检查中间件
 */
async function requireAdmin(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录'
      });
    }
    
    const users = await db.query(
      'SELECT username FROM users WHERE id = ? AND username = ?',
      [req.user.userId, 'admin']
    );
    
    if (users.length === 0) {
      return res.status(403).json({
        error: '需要管理员权限'
      });
    }
    
    next();
  } catch (error) {
    console.error('权限检查失败:', error);
    res.status(500).json({
      error: '权限检查失败'
    });
  }
}

/**
 * 用户状态检查中间件
 */
async function checkUserStatus(req, res, next) {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录'
      });
    }
    
    const users = await db.query(
      'SELECT is_active FROM users WHERE id = ?',
      [req.user.userId]
    );
    
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    if (!users[0].is_active) {
      return res.status(403).json({
        error: '账户已被禁用'
      });
    }
    
    next();
  } catch (error) {
    console.error('用户状态检查失败:', error);
    res.status(500).json({
      error: '用户状态检查失败'
    });
  }
}

/**
 * 资源所有权检查中间件
 */
function checkResourceOwnership(resourceType, idParam = 'id') {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: '需要登录'
        });
      }
      
      const resourceId = req.params[idParam];
      if (!resourceId) {
        return res.status(400).json({
          error: '资源ID缺失'
        });
      }
      
      let tableName;
      switch (resourceType) {
        case 'memo':
          tableName = 'memos';
          break;
        case 'setting':
          tableName = 'user_settings';
          break;
        default:
          return res.status(400).json({
            error: '不支持的资源类型'
          });
      }
      
      const resources = await db.query(
        `SELECT user_id FROM ${tableName} WHERE id = ?`,
        [resourceId]
      );
      
      if (resources.length === 0) {
        return res.status(404).json({
          error: '资源不存在'
        });
      }
      
      if (resources[0].user_id !== req.user.userId) {
        return res.status(403).json({
          error: '无权访问此资源'
        });
      }
      
      next();
    } catch (error) {
      console.error('资源所有权检查失败:', error);
      res.status(500).json({
        error: '资源所有权检查失败'
      });
    }
  };
}

/**
 * 请求日志中间件
 */
async function logRequest(req, res, next) {
  try {
    const userId = req.user ? req.user.userId : null;
    const action = `${req.method} ${req.path}`;
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent');
    
    // 异步记录日志，不阻塞请求
    setImmediate(async () => {
      try {
        await db.query(
          'INSERT INTO system_logs (user_id, action, ip_address, user_agent, details) VALUES (?, ?, ?, ?, ?)',
          [userId, action, ipAddress, userAgent, JSON.stringify({
            params: req.params,
            query: req.query,
            body: req.method === 'POST' || req.method === 'PUT' ? req.body : undefined
          })]
        );
      } catch (logError) {
        console.error('记录请求日志失败:', logError);
      }
    });
    
    next();
  } catch (error) {
    console.error('请求日志中间件错误:', error);
    next(); // 即使日志记录失败也要继续处理请求
  }
}

module.exports = {
  authenticateToken,
  optionalAuth,
  requireAdmin,
  checkUserStatus,
  checkResourceOwnership,
  logRequest
};
