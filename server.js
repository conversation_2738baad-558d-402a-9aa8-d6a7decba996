const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

// 导入数据库
const db = require('./config/database');

// 导入路由
const authRoutes = require('./routes/auth');
const memoRoutes = require('./routes/memos');
const holidayRoutes = require('./routes/holidays');
const userRoutes = require('./routes/users');
const settingsRoutes = require('./routes/settings');

const app = express();
const PORT = process.env.PORT || 3000;

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
      connectSrc: ["'self'"]
    }
  }
}));

// 跨域配置
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com'] 
    : ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// 请求限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

// 解析请求体
app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/memos', memoRoutes);
app.use('/api/holidays', holidayRoutes);
app.use('/api/users', userRoutes);
app.use('/api/settings', settingsRoutes);

// 健康检查端点
app.get('/api/health', async (req, res) => {
  try {
    const isDbConnected = await db.testConnection();
    const stats = await db.getStats();
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: isDbConnected ? 'connected' : 'disconnected',
      stats: stats,
      version: '1.0.0'
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: '健康检查失败',
      error: error.message
    });
  }
});

// API信息端点
app.get('/api/info', (req, res) => {
  res.json({
    name: '智能日历系统 API',
    version: '1.0.0',
    description: '基于MySQL的智能日历系统后端API',
    endpoints: {
      auth: '/api/auth',
      memos: '/api/memos',
      holidays: '/api/holidays',
      users: '/api/users',
      settings: '/api/settings'
    },
    documentation: '/api/docs'
  });
});

// 前端路由 - 所有非API请求都返回index.html
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error);
  
  res.status(error.status || 500).json({
    error: process.env.NODE_ENV === 'production' 
      ? '服务器内部错误' 
      : error.message,
    timestamp: new Date().toISOString()
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '请求的资源不存在',
    path: req.path,
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    const isDbConnected = await db.testConnection();
    if (!isDbConnected) {
      console.error('❌ 数据库连接失败，服务器启动中止');
      process.exit(1);
    }
    
    // 启动HTTP服务器
    const server = app.listen(PORT, () => {
      console.log(`
🚀 智能日历系统服务器启动成功！

📍 服务器地址: http://localhost:${PORT}
📍 API地址: http://localhost:${PORT}/api
📍 健康检查: http://localhost:${PORT}/api/health
📍 API信息: http://localhost:${PORT}/api/info

🔧 环境: ${process.env.NODE_ENV || 'development'}
💾 数据库: ${process.env.DB_HOST}:${process.env.DB_PORT}/${process.env.DB_NAME}

按 Ctrl+C 停止服务器
      `);
    });
    
    // 优雅关闭
    process.on('SIGTERM', async () => {
      console.log('收到SIGTERM信号，正在优雅关闭服务器...');
      server.close(async () => {
        await db.close();
        console.log('服务器已关闭');
        process.exit(0);
      });
    });
    
    process.on('SIGINT', async () => {
      console.log('\n收到SIGINT信号，正在优雅关闭服务器...');
      server.close(async () => {
        await db.close();
        console.log('服务器已关闭');
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
startServer();

module.exports = app;
