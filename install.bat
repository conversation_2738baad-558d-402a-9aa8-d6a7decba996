@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    智能日历系统 - MySQL版 安装脚本
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/5] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 检查MySQL是否运行
echo.
echo [2/5] 检查MySQL服务...
sc query mysql >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ MySQL服务未运行，请启动MySQL服务
    echo 或者检查MySQL是否已正确安装
    pause
    exit /b 1
)
echo ✅ MySQL服务检查通过

:: 安装依赖
echo.
echo [3/5] 安装项目依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

:: 初始化数据库
echo.
echo [4/5] 初始化数据库...
npm run init-db
if %errorlevel% neq 0 (
    echo ❌ 数据库初始化失败
    echo 请检查数据库连接配置 (.env文件)
    pause
    exit /b 1
)
echo ✅ 数据库初始化完成

:: 创建启动脚本
echo.
echo [5/5] 创建启动脚本...
echo @echo off > start.bat
echo chcp 65001 ^>nul >> start.bat
echo echo. >> start.bat
echo echo ======================================== >> start.bat
echo echo    智能日历系统 - MySQL版 >> start.bat
echo echo ======================================== >> start.bat
echo echo. >> start.bat
echo echo 🚀 正在启动服务器... >> start.bat
echo echo. >> start.bat
echo npm start >> start.bat
echo ✅ 启动脚本创建完成

echo.
echo ========================================
echo           🎉 安装完成！
echo ========================================
echo.
echo 📍 服务器地址: http://localhost:3000
echo 📍 默认用户: admin
echo 📍 默认密码: 123456
echo.
echo 💡 使用说明:
echo    1. 运行 start.bat 启动服务器
echo    2. 在浏览器中访问 http://localhost:3000
echo    3. 使用默认账户登录系统
echo.
echo 🔧 配置文件: .env
echo 📚 文档地址: README.md
echo.
set /p choice="是否现在启动服务器？(Y/N): "
if /i "%choice%"=="Y" (
    echo.
    echo 🚀 正在启动服务器...
    npm start
) else (
    echo.
    echo 💡 稍后可以运行 start.bat 启动服务器
)

pause
