const express = require('express');
const db = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取用户列表 (需要管理员权限)
 */
router.get('/', requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, search, status } = req.query;
    
    let sql = 'SELECT id, username, email, full_name, avatar_url, timezone, language, theme, created_at, last_login_at, is_active FROM users WHERE 1=1';
    let params = [];
    
    // 搜索条件
    if (search) {
      sql += ' AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    // 状态筛选
    if (status !== undefined) {
      sql += ' AND is_active = ?';
      params.push(status === 'active');
    }
    
    // 排序
    sql += ' ORDER BY created_at DESC';
    
    // 分页
    const offset = (page - 1) * limit;
    sql += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const users = await db.query(sql, params);
    
    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM users WHERE 1=1';
    let countParams = [];
    
    if (search) {
      countSql += ' AND (username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }
    
    if (status !== undefined) {
      countSql += ' AND is_active = ?';
      countParams.push(status === 'active');
    }
    
    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;
    
    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      error: '获取用户列表失败'
    });
  }
});

/**
 * 获取用户统计信息 (需要管理员权限)
 */
router.get('/stats', requireAdmin, async (req, res) => {
  try {
    // 总用户数
    const totalResult = await db.query('SELECT COUNT(*) as total FROM users');
    
    // 活跃用户数
    const activeResult = await db.query('SELECT COUNT(*) as active FROM users WHERE is_active = true');
    
    // 今日注册用户数
    const todayResult = await db.query('SELECT COUNT(*) as today FROM users WHERE DATE(created_at) = CURDATE()');
    
    // 本月注册用户数
    const monthResult = await db.query('SELECT COUNT(*) as month FROM users WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())');
    
    // 最近7天注册趋势
    const weeklyTrend = await db.query(`
      SELECT DATE(created_at) as date, COUNT(*) as count 
      FROM users 
      WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `);
    
    res.json({
      total: totalResult[0].total,
      active: activeResult[0].active,
      today: todayResult[0].today,
      month: monthResult[0].month,
      weeklyTrend
    });
    
  } catch (error) {
    console.error('获取用户统计失败:', error);
    res.status(500).json({
      error: '获取用户统计失败'
    });
  }
});

/**
 * 获取指定用户信息 (需要管理员权限)
 */
router.get('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    const users = await db.query(
      'SELECT id, username, email, full_name, avatar_url, timezone, language, theme, created_at, last_login_at, is_active FROM users WHERE id = ?',
      [id]
    );
    
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    // 获取用户的备忘录统计
    const memoStats = await db.query(
      'SELECT COUNT(*) as total, SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed FROM memos WHERE user_id = ?',
      [id]
    );
    
    res.json({
      user: {
        ...users[0],
        memoStats: memoStats[0]
      }
    });
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      error: '获取用户信息失败'
    });
  }
});

/**
 * 更新用户状态 (需要管理员权限)
 */
router.put('/:id/status', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;
    
    // 检查用户是否存在
    const users = await db.query('SELECT id FROM users WHERE id = ?', [id]);
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    await db.query(
      'UPDATE users SET is_active = ? WHERE id = ?',
      [isActive, id]
    );
    
    res.json({
      message: `用户已${isActive ? '激活' : '禁用'}`
    });
    
  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      error: '更新用户状态失败'
    });
  }
});

/**
 * 删除用户 (需要管理员权限)
 */
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const adminUserId = req.user.userId;
    
    // 不能删除自己
    if (parseInt(id) === adminUserId) {
      return res.status(400).json({
        error: '不能删除自己的账户'
      });
    }
    
    // 检查用户是否存在
    const users = await db.query('SELECT id FROM users WHERE id = ?', [id]);
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    // 删除用户（级联删除相关数据）
    await db.query('DELETE FROM users WHERE id = ?', [id]);
    
    res.json({
      message: '用户删除成功'
    });
    
  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      error: '删除用户失败'
    });
  }
});

module.exports = router;
