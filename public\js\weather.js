/**
 * 天气预报模块
 */
class WeatherManager {
  constructor() {
    this.widget = null;
    this.isVisible = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.weatherData = null;
    
    this.init();
  }

  /**
   * 初始化天气管理器
   */
  init() {
    this.widget = document.getElementById('weatherWidget');
    this.bindEvents();
    this.initDragAndDrop();
    this.createWeatherContent();
  }

  /**
   * 创建天气内容
   */
  createWeatherContent() {
    const content = document.querySelector('.weather-content');
    content.innerHTML = `
      <!-- 加载状态 -->
      <div class="weather-loading text-center py-4" id="weatherLoading">
        <div class="spinner-border text-primary mb-3" role="status"></div>
        <p class="mb-0">正在获取天气信息...</p>
      </div>
      
      <!-- 天气信息 -->
      <div class="weather-info" id="weatherInfo" style="display: none;">
        <!-- 当前天气 -->
        <div class="current-weather mb-4">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h5 class="mb-1" id="weatherLocation">北京市</h5>
              <small class="text-muted" id="weatherTime">2025-01-16 14:30</small>
            </div>
            <div class="text-end">
              <div class="h3 mb-0 text-primary" id="weatherTemp">15°C</div>
              <small class="text-muted" id="weatherCondition">晴</small>
            </div>
          </div>
        </div>
        
        <!-- 天气详情 -->
        <div class="weather-details mb-4">
          <div class="row g-3">
            <div class="col-6">
              <div class="detail-item p-2 bg-light rounded">
                <i class="bi bi-droplet text-info"></i>
                <span class="ms-2">湿度</span>
                <strong class="float-end" id="weatherHumidity">65%</strong>
              </div>
            </div>
            <div class="col-6">
              <div class="detail-item p-2 bg-light rounded">
                <i class="bi bi-wind text-success"></i>
                <span class="ms-2">风速</span>
                <strong class="float-end" id="weatherWind">8.5 km/h</strong>
              </div>
            </div>
            <div class="col-6">
              <div class="detail-item p-2 bg-light rounded">
                <i class="bi bi-thermometer text-warning"></i>
                <span class="ms-2">体感</span>
                <strong class="float-end" id="weatherFeels">16°C</strong>
              </div>
            </div>
            <div class="col-6">
              <div class="detail-item p-2 bg-light rounded">
                <i class="bi bi-eye text-secondary"></i>
                <span class="ms-2">能见度</span>
                <strong class="float-end" id="weatherVisibility">10km</strong>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 未来天气 -->
        <div class="forecast">
          <h6 class="mb-3">未来三天</h6>
          <div class="row g-2" id="weatherForecast">
            <!-- 预报数据将通过JavaScript填充 -->
          </div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div class="weather-error text-center py-4" id="weatherError" style="display: none;">
        <i class="bi bi-exclamation-triangle text-warning fs-1 mb-3"></i>
        <p class="mb-3">无法获取天气信息</p>
        <button class="btn btn-primary btn-sm" id="retryWeather">
          <i class="bi bi-arrow-clockwise me-1"></i>重新获取
        </button>
      </div>
    `;
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 天气按钮点击
    document.getElementById('weatherBtn').addEventListener('click', () => {
      this.toggle();
    });

    // 关闭按钮
    document.getElementById('closeWeather').addEventListener('click', () => {
      this.hide();
    });

    // 重试按钮（使用事件委托）
    document.addEventListener('click', (e) => {
      if (e.target.id === 'retryWeather') {
        this.loadWeather();
      }
    });

    // 点击外部关闭
    document.addEventListener('click', (e) => {
      if (this.isVisible && 
          !this.widget.contains(e.target) && 
          !document.getElementById('weatherBtn').contains(e.target)) {
        this.hide();
      }
    });

    // 键盘事件
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  /**
   * 初始化拖拽功能
   */
  initDragAndDrop() {
    const header = this.widget.querySelector('.weather-header');
    
    header.addEventListener('mousedown', (e) => {
      this.isDragging = true;
      const rect = this.widget.getBoundingClientRect();
      this.dragOffset = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
      
      document.addEventListener('mousemove', this.handleDrag.bind(this));
      document.addEventListener('mouseup', this.handleDragEnd.bind(this));
      
      e.preventDefault();
    });
  }

  /**
   * 处理拖拽
   */
  handleDrag(e) {
    if (!this.isDragging) return;
    
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;
    
    // 限制在窗口范围内
    const maxX = window.innerWidth - this.widget.offsetWidth;
    const maxY = window.innerHeight - this.widget.offsetHeight;
    
    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));
    
    this.widget.style.left = constrainedX + 'px';
    this.widget.style.top = constrainedY + 'px';
    this.widget.style.right = 'auto';
    this.widget.style.transform = 'none';
  }

  /**
   * 结束拖拽
   */
  handleDragEnd() {
    this.isDragging = false;
    document.removeEventListener('mousemove', this.handleDrag);
    document.removeEventListener('mouseup', this.handleDragEnd);
  }

  /**
   * 切换显示/隐藏
   */
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  /**
   * 显示天气窗口
   */
  show() {
    this.isVisible = true;
    this.widget.classList.add('show');
    
    // 如果没有天气数据，加载天气
    if (!this.weatherData) {
      this.loadWeather();
    }
  }

  /**
   * 隐藏天气窗口
   */
  hide() {
    this.isVisible = false;
    this.widget.classList.remove('show');
  }

  /**
   * 加载天气数据
   */
  async loadWeather() {
    this.showLoading();
    
    try {
      // 模拟天气数据
      const weatherData = await this.fetchWeatherData();
      this.weatherData = weatherData;
      this.displayWeather(weatherData);
      
    } catch (error) {
      console.error('获取天气失败:', error);
      this.showError();
    }
  }

  /**
   * 获取天气数据（模拟）
   */
  async fetchWeatherData() {
    return new Promise((resolve) => {
      setTimeout(() => {
        const conditions = ['晴', '多云', '阴', '小雨', '中雨', '大雨', '雷阵雨', '雪', '雾', '霾'];
        const cities = ['北京市', '上海市', '广州市', '深圳市', '杭州市', '南京市', '成都市', '武汉市'];
        
        const weatherData = {
          location: cities[Math.floor(Math.random() * cities.length)],
          temperature: Math.floor(Math.random() * 30) + 5, // 5-35度
          condition: conditions[Math.floor(Math.random() * conditions.length)],
          humidity: Math.floor(Math.random() * 40) + 40, // 40-80%
          windSpeed: (Math.random() * 15 + 2).toFixed(1), // 2-17 km/h
          feelsLike: Math.floor(Math.random() * 30) + 5,
          visibility: Math.floor(Math.random() * 15) + 5, // 5-20km
          updateTime: new Date().toLocaleString('zh-CN'),
          forecast: this.generateForecast()
        };
        
        resolve(weatherData);
      }, 1000 + Math.random() * 1000); // 1-2秒延迟
    });
  }

  /**
   * 生成未来天气预报
   */
  generateForecast() {
    const conditions = ['晴', '多云', '阴', '小雨', '雪'];
    const forecast = [];
    
    for (let i = 1; i <= 3; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      const dayNames = ['明天', '后天', '大后天'];
      
      forecast.push({
        day: dayNames[i - 1],
        date: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        condition: conditions[Math.floor(Math.random() * conditions.length)],
        high: Math.floor(Math.random() * 25) + 15, // 15-40度
        low: Math.floor(Math.random() * 15) + 5    // 5-20度
      });
    }
    
    return forecast;
  }

  /**
   * 显示加载状态
   */
  showLoading() {
    document.getElementById('weatherLoading').style.display = 'block';
    document.getElementById('weatherInfo').style.display = 'none';
    document.getElementById('weatherError').style.display = 'none';
  }

  /**
   * 显示天气信息
   */
  displayWeather(data) {
    // 更新当前天气
    document.getElementById('weatherLocation').textContent = data.location;
    document.getElementById('weatherTime').textContent = data.updateTime;
    document.getElementById('weatherTemp').textContent = `${data.temperature}°C`;
    document.getElementById('weatherCondition').textContent = data.condition;
    document.getElementById('weatherHumidity').textContent = `${data.humidity}%`;
    document.getElementById('weatherWind').textContent = `${data.windSpeed} km/h`;
    document.getElementById('weatherFeels').textContent = `${data.feelsLike}°C`;
    document.getElementById('weatherVisibility').textContent = `${data.visibility}km`;
    
    // 更新预报
    const forecastContainer = document.getElementById('weatherForecast');
    forecastContainer.innerHTML = '';
    
    data.forecast.forEach(day => {
      const dayElement = document.createElement('div');
      dayElement.className = 'col-4';
      dayElement.innerHTML = `
        <div class="card h-100">
          <div class="card-body p-2 text-center">
            <small class="text-muted d-block">${day.day}</small>
            <small class="d-block">${day.date}</small>
            <small class="d-block fw-bold">${day.condition}</small>
            <small class="text-primary">${day.high}°/${day.low}°</small>
          </div>
        </div>
      `;
      forecastContainer.appendChild(dayElement);
    });
    
    // 显示天气信息
    document.getElementById('weatherLoading').style.display = 'none';
    document.getElementById('weatherInfo').style.display = 'block';
    document.getElementById('weatherError').style.display = 'none';
  }

  /**
   * 显示错误状态
   */
  showError() {
    document.getElementById('weatherLoading').style.display = 'none';
    document.getElementById('weatherInfo').style.display = 'none';
    document.getElementById('weatherError').style.display = 'block';
  }
}

// 全局天气管理器实例
window.weatherManager = null;
