const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const db = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

/**
 * 用户注册
 */
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, fullName } = req.body;
    
    // 验证输入
    if (!username || !email || !password) {
      return res.status(400).json({
        error: '用户名、邮箱和密码不能为空'
      });
    }
    
    // 检查用户名和邮箱是否已存在
    const existingUser = await db.query(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );
    
    if (existingUser.length > 0) {
      return res.status(409).json({
        error: '用户名或邮箱已存在'
      });
    }
    
    // 加密密码
    const passwordHash = await bcrypt.hash(password, 10);
    
    // 创建用户
    const result = await db.query(
      'INSERT INTO users (username, email, password_hash, full_name) VALUES (?, ?, ?, ?)',
      [username, email, passwordHash, fullName || username]
    );
    
    const userId = result.insertId;
    
    // 生成JWT token
    const token = jwt.sign(
      { userId, username, email },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // 记录登录时间
    await db.query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?',
      [userId]
    );
    
    res.status(201).json({
      message: '注册成功',
      token,
      user: {
        id: userId,
        username,
        email,
        fullName: fullName || username
      }
    });
    
  } catch (error) {
    console.error('注册失败:', error);
    res.status(500).json({
      error: '注册失败，请稍后重试'
    });
  }
});

/**
 * 用户登录
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 验证输入
    if (!username || !password) {
      return res.status(400).json({
        error: '用户名和密码不能为空'
      });
    }
    
    // 查找用户
    const users = await db.query(
      'SELECT id, username, email, password_hash, full_name, is_active FROM users WHERE username = ? OR email = ?',
      [username, username]
    );
    
    if (users.length === 0) {
      return res.status(401).json({
        error: '用户名或密码错误'
      });
    }
    
    const user = users[0];
    
    // 检查用户是否激活
    if (!user.is_active) {
      return res.status(401).json({
        error: '账户已被禁用'
      });
    }
    
    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      return res.status(401).json({
        error: '用户名或密码错误'
      });
    }
    
    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username, 
        email: user.email 
      },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    // 更新最后登录时间
    await db.query(
      'UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = ?',
      [user.id]
    );
    
    res.json({
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        fullName: user.full_name
      }
    });
    
  } catch (error) {
    console.error('登录失败:', error);
    res.status(500).json({
      error: '登录失败，请稍后重试'
    });
  }
});

/**
 * 获取当前用户信息
 */
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const users = await db.query(
      'SELECT id, username, email, full_name, avatar_url, timezone, language, theme, created_at, last_login_at FROM users WHERE id = ?',
      [req.user.userId]
    );
    
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    res.json({
      user: users[0]
    });
    
  } catch (error) {
    console.error('获取用户信息失败:', error);
    res.status(500).json({
      error: '获取用户信息失败'
    });
  }
});

/**
 * 更新用户信息
 */
router.put('/profile', authenticateToken, async (req, res) => {
  try {
    const { email, fullName, timezone, language, theme } = req.body;
    const userId = req.user.userId;

    // 检查邮箱是否已被其他用户使用
    if (email) {
      const existingUsers = await db.query(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, userId]
      );

      if (existingUsers.length > 0) {
        return res.status(409).json({
          error: '邮箱已被其他用户使用'
        });
      }
    }

    await db.query(
      'UPDATE users SET email = ?, full_name = ?, timezone = ?, language = ?, theme = ? WHERE id = ?',
      [email, fullName, timezone, language, theme, userId]
    );

    res.json({
      message: '用户信息更新成功'
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      error: '更新用户信息失败'
    });
  }
});

/**
 * 修改密码
 */
router.put('/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;
    
    // 验证输入
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        error: '当前密码和新密码不能为空'
      });
    }
    
    // 获取当前密码哈希
    const users = await db.query(
      'SELECT password_hash FROM users WHERE id = ?',
      [userId]
    );
    
    if (users.length === 0) {
      return res.status(404).json({
        error: '用户不存在'
      });
    }
    
    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, users[0].password_hash);
    if (!isCurrentPasswordValid) {
      return res.status(401).json({
        error: '当前密码错误'
      });
    }
    
    // 加密新密码
    const newPasswordHash = await bcrypt.hash(newPassword, 10);
    
    // 更新密码
    await db.query(
      'UPDATE users SET password_hash = ? WHERE id = ?',
      [newPasswordHash, userId]
    );
    
    res.json({
      message: '密码修改成功'
    });
    
  } catch (error) {
    console.error('修改密码失败:', error);
    res.status(500).json({
      error: '修改密码失败'
    });
  }
});

/**
 * 刷新token
 */
router.post('/refresh', authenticateToken, async (req, res) => {
  try {
    const { userId, username, email } = req.user;
    
    // 生成新的token
    const newToken = jwt.sign(
      { userId, username, email },
      process.env.JWT_SECRET,
      { expiresIn: '7d' }
    );
    
    res.json({
      message: 'Token刷新成功',
      token: newToken
    });
    
  } catch (error) {
    console.error('Token刷新失败:', error);
    res.status(500).json({
      error: 'Token刷新失败'
    });
  }
});

/**
 * 登出
 */
router.post('/logout', authenticateToken, async (req, res) => {
  try {
    // 这里可以实现token黑名单机制
    // 目前只是返回成功消息
    
    res.json({
      message: '登出成功'
    });
    
  } catch (error) {
    console.error('登出失败:', error);
    res.status(500).json({
      error: '登出失败'
    });
  }
});

module.exports = router;
