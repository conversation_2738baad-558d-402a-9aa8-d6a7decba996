<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>登录功能测试
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            这是一个简化的登录测试页面，用于验证登录功能是否正常工作。
                        </div>

                        <!-- 登录表单 -->
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-1"></i>用户名或邮箱
                                </label>
                                <input type="text" class="form-control" id="username" required
                                       placeholder="请输入用户名或邮箱" value="admin">
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>密码
                                </label>
                                <input type="password" class="form-control" id="password" required
                                       placeholder="请输入密码" value="123456">
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-1"></i>登录
                                </button>
                            </div>
                        </form>

                        <!-- 状态显示 -->
                        <div id="status" class="mt-3"></div>
                        
                        <!-- 用户信息显示 -->
                        <div id="userInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-success">
                                <h6><i class="bi bi-person-check me-2"></i>登录成功</h6>
                                <p class="mb-1"><strong>用户名：</strong><span id="displayUsername"></span></p>
                                <p class="mb-1"><strong>邮箱：</strong><span id="displayEmail"></span></p>
                                <p class="mb-0"><strong>姓名：</strong><span id="displayFullName"></span></p>
                            </div>
                            <button type="button" class="btn btn-outline-danger" onclick="logout()">
                                <i class="bi bi-box-arrow-right me-1"></i>退出登录
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 测试说明 -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>测试说明</h6>
                    </div>
                    <div class="card-body">
                        <h6>默认测试账户：</h6>
                        <ul>
                            <li><strong>用户名：</strong>admin</li>
                            <li><strong>密码：</strong>123456</li>
                        </ul>
                        <h6>测试步骤：</h6>
                        <ol>
                            <li>输入用户名和密码（已预填）</li>
                            <li>点击"登录"按钮</li>
                            <li>查看登录结果和用户信息</li>
                            <li>测试退出登录功能</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 登录功能
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('status');
            
            // 显示加载状态
            statusDiv.innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-hourglass-split me-2"></i>正在登录...
                </div>
            `;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    // 登录成功
                    statusDiv.innerHTML = `
                        <div class="alert alert-success">
                            <i class="bi bi-check-circle me-2"></i>${data.message}
                        </div>
                    `;
                    
                    // 保存token
                    localStorage.setItem('authToken', data.token);
                    
                    // 显示用户信息
                    document.getElementById('displayUsername').textContent = data.user.username;
                    document.getElementById('displayEmail').textContent = data.user.email;
                    document.getElementById('displayFullName').textContent = data.user.fullName || data.user.full_name;
                    document.getElementById('userInfo').style.display = 'block';
                    
                    // 隐藏登录表单
                    document.getElementById('loginForm').style.display = 'none';
                    
                } else {
                    // 登录失败
                    statusDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle me-2"></i>${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('登录错误:', error);
                statusDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>登录失败，请检查网络连接
                    </div>
                `;
            }
        });
        
        // 退出登录功能
        function logout() {
            localStorage.removeItem('authToken');
            document.getElementById('userInfo').style.display = 'none';
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('status').innerHTML = `
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>已退出登录
                </div>
            `;
        }
        
        // 页面加载时检查登录状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('authToken');
            if (token) {
                // 验证token是否有效
                fetch('/api/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.user) {
                        // 已登录，显示用户信息
                        document.getElementById('displayUsername').textContent = data.user.username;
                        document.getElementById('displayEmail').textContent = data.user.email;
                        document.getElementById('displayFullName').textContent = data.user.fullName || data.user.full_name;
                        document.getElementById('userInfo').style.display = 'block';
                        document.getElementById('loginForm').style.display = 'none';
                        document.getElementById('status').innerHTML = `
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle me-2"></i>已登录状态
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('验证token失败:', error);
                    localStorage.removeItem('authToken');
                });
            }
        });
    </script>
</body>
</html>
