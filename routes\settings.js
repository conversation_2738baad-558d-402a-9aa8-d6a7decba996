const express = require('express');
const db = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * 获取用户所有设置
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const settings = await db.query(
      'SELECT setting_key, setting_value FROM user_settings WHERE user_id = ?',
      [userId]
    );
    
    // 转换为键值对对象
    const settingsObj = settings.reduce((acc, setting) => {
      acc[setting.setting_key] = setting.setting_value;
      return acc;
    }, {});
    
    res.json({
      settings: settingsObj
    });
    
  } catch (error) {
    console.error('获取用户设置失败:', error);
    res.status(500).json({
      error: '获取用户设置失败'
    });
  }
});

/**
 * 获取指定设置项
 */
router.get('/:key', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { key } = req.params;
    
    const settings = await db.query(
      'SELECT setting_value FROM user_settings WHERE user_id = ? AND setting_key = ?',
      [userId, key]
    );
    
    if (settings.length === 0) {
      return res.status(404).json({
        error: '设置项不存在'
      });
    }
    
    res.json({
      key,
      value: settings[0].setting_value
    });
    
  } catch (error) {
    console.error('获取设置项失败:', error);
    res.status(500).json({
      error: '获取设置项失败'
    });
  }
});

/**
 * 设置单个配置项
 */
router.put('/:key', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { key } = req.params;
    const { value } = req.body;
    
    if (value === undefined) {
      return res.status(400).json({
        error: '设置值不能为空'
      });
    }
    
    // 使用 ON DUPLICATE KEY UPDATE 实现 upsert
    await db.query(
      'INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)',
      [userId, key, JSON.stringify(value)]
    );
    
    res.json({
      message: '设置保存成功',
      key,
      value
    });
    
  } catch (error) {
    console.error('保存设置失败:', error);
    res.status(500).json({
      error: '保存设置失败'
    });
  }
});

/**
 * 批量设置配置项
 */
router.put('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { settings } = req.body;
    
    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        error: '设置数据格式错误'
      });
    }
    
    // 使用事务批量更新
    await db.transaction(async (connection) => {
      for (const [key, value] of Object.entries(settings)) {
        await connection.execute(
          'INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)',
          [userId, key, JSON.stringify(value)]
        );
      }
    });
    
    res.json({
      message: '批量设置保存成功',
      count: Object.keys(settings).length
    });
    
  } catch (error) {
    console.error('批量保存设置失败:', error);
    res.status(500).json({
      error: '批量保存设置失败'
    });
  }
});

/**
 * 删除设置项
 */
router.delete('/:key', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { key } = req.params;
    
    const result = await db.query(
      'DELETE FROM user_settings WHERE user_id = ? AND setting_key = ?',
      [userId, key]
    );
    
    if (result.affectedRows === 0) {
      return res.status(404).json({
        error: '设置项不存在'
      });
    }
    
    res.json({
      message: '设置项删除成功'
    });
    
  } catch (error) {
    console.error('删除设置项失败:', error);
    res.status(500).json({
      error: '删除设置项失败'
    });
  }
});

/**
 * 重置所有设置
 */
router.delete('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    
    await db.query(
      'DELETE FROM user_settings WHERE user_id = ?',
      [userId]
    );
    
    res.json({
      message: '所有设置已重置'
    });
    
  } catch (error) {
    console.error('重置设置失败:', error);
    res.status(500).json({
      error: '重置设置失败'
    });
  }
});

/**
 * 导出用户设置
 */
router.get('/export/all', async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const settings = await db.query(
      'SELECT setting_key, setting_value, created_at, updated_at FROM user_settings WHERE user_id = ?',
      [userId]
    );
    
    const exportData = {
      userId,
      exportDate: new Date().toISOString(),
      settings: settings.reduce((acc, setting) => {
        acc[setting.setting_key] = {
          value: setting.setting_value,
          createdAt: setting.created_at,
          updatedAt: setting.updated_at
        };
        return acc;
      }, {})
    };
    
    res.json(exportData);
    
  } catch (error) {
    console.error('导出设置失败:', error);
    res.status(500).json({
      error: '导出设置失败'
    });
  }
});

/**
 * 导入用户设置
 */
router.post('/import', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { settings, overwrite = false } = req.body;
    
    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        error: '导入数据格式错误'
      });
    }
    
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    
    await db.transaction(async (connection) => {
      for (const [key, settingData] of Object.entries(settings)) {
        try {
          const value = settingData.value || settingData;
          
          if (overwrite) {
            // 覆盖模式：直接插入或更新
            await connection.execute(
              'INSERT INTO user_settings (user_id, setting_key, setting_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)',
              [userId, key, JSON.stringify(value)]
            );
          } else {
            // 非覆盖模式：只插入不存在的设置
            await connection.execute(
              'INSERT IGNORE INTO user_settings (user_id, setting_key, setting_value) VALUES (?, ?, ?)',
              [userId, key, JSON.stringify(value)]
            );
          }
          
          successCount++;
        } catch (error) {
          errors.push(`设置项 ${key}: ${error.message}`);
          errorCount++;
        }
      }
    });
    
    res.json({
      message: '设置导入完成',
      success: successCount,
      errors: errorCount,
      details: errors
    });
    
  } catch (error) {
    console.error('导入设置失败:', error);
    res.status(500).json({
      error: '导入设置失败'
    });
  }
});

/**
 * 获取默认设置模板
 */
router.get('/templates/default', async (req, res) => {
  try {
    const defaultSettings = {
      theme: 'light',
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      timeFormat: '24h',
      weekStart: 1, // 1 = Monday, 0 = Sunday
      notifications: {
        email: true,
        browser: true,
        sound: true
      },
      calendar: {
        defaultView: 'month',
        showWeekends: true,
        showLunar: true,
        showHolidays: true
      },
      memo: {
        defaultPriority: 'medium',
        defaultCategory: 'other',
        autoReminder: false,
        reminderMinutes: 15
      },
      privacy: {
        shareCalendar: false,
        allowSearch: true
      }
    };
    
    res.json({
      template: 'default',
      settings: defaultSettings
    });
    
  } catch (error) {
    console.error('获取默认设置模板失败:', error);
    res.status(500).json({
      error: '获取默认设置模板失败'
    });
  }
});

module.exports = router;
