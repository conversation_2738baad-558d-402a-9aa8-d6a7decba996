@echo off
echo ========================================
echo 智能日历系统 - 数据库初始化脚本
echo ========================================
echo.

echo 正在检查MySQL是否安装...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到MySQL命令行工具
    echo 请确保MySQL已安装并添加到系统PATH中
    pause
    exit /b 1
)

echo ✅ MySQL已找到

echo.
echo 请输入MySQL root用户密码:
set /p mysql_password=密码: 

echo.
echo 正在创建数据库和表结构...
mysql -u root -p%mysql_password% < database\smart_calendar.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ 数据库初始化成功！
    echo.
    echo 📋 数据库信息:
    echo    数据库名: smart_calendar
    echo    默认用户: admin
    echo    默认密码: 123456
    echo.
    echo 🚀 现在可以启动服务器了:
    echo    npm start
    echo    或者
    echo    node server.js
    echo.
) else (
    echo.
    echo ❌ 数据库初始化失败！
    echo 请检查:
    echo 1. MySQL服务是否正在运行
    echo 2. 用户名和密码是否正确
    echo 3. database\smart_calendar.sql 文件是否存在
    echo.
)

pause
