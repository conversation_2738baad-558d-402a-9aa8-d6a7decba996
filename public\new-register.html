<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能日历系统 - 注册</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 48px;
            width: 100%;
            max-width: 480px;
            position: relative;
            overflow: hidden;
        }

        .register-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 300% 100%;
            animation: gradient 3s ease infinite;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .logo {
            text-align: center;
            margin-bottom: 32px;
        }

        .logo-icon {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
        }

        .logo-icon i {
            font-size: 28px;
            color: white;
        }

        .logo h1 {
            font-size: 28px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .logo p {
            color: #6b7280;
            font-size: 16px;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            color: #1f2937;
            background: #ffffff;
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: #9ca3af;
        }

        .input-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 18px;
            pointer-events: none;
        }

        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #9ca3af;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .password-toggle:hover {
            color: #667eea;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }

        .strength-bar {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            margin: 4px 0;
            overflow: hidden;
        }

        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #ef4444; width: 25%; }
        .strength-fair { background: #f59e0b; width: 50%; }
        .strength-good { background: #10b981; width: 75%; }
        .strength-strong { background: #059669; width: 100%; }

        .terms-group {
            margin: 24px 0;
        }

        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .checkbox:checked {
            background: #667eea;
            border-color: #667eea;
        }

        .checkbox:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: 600;
        }

        .checkbox-label {
            font-size: 14px;
            color: #6b7280;
            cursor: pointer;
            line-height: 1.5;
        }

        .checkbox-label a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .checkbox-label a:hover {
            text-decoration: underline;
        }

        .register-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 24px;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.4);
        }

        .register-btn:active {
            transform: translateY(0);
        }

        .register-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-loading {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .login-link {
            text-align: center;
        }

        .login-link p {
            color: #6b7280;
            font-size: 14px;
        }

        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .login-link a:hover {
            color: #5a67d8;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .form-help {
            font-size: 12px;
            color: #6b7280;
            margin-top: 4px;
        }

        @media (max-width: 480px) {
            .register-container {
                padding: 32px 24px;
                margin: 16px;
            }

            .logo h1 {
                font-size: 24px;
            }

            .form-input {
                padding: 14px 16px;
                font-size: 16px;
            }

            .form-row {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="logo">
            <div class="logo-icon">
                <i class="bi bi-person-plus"></i>
            </div>
            <h1>创建账户</h1>
            <p>加入智能日历，开始管理您的时间</p>
        </div>

        <!-- 状态提示 -->
        <div id="alertContainer"></div>

        <!-- 注册表单 -->
        <form id="registerForm">
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label" for="firstName">
                        <i class="bi bi-person"></i> 姓
                    </label>
                    <div style="position: relative;">
                        <input type="text" id="firstName" class="form-input" placeholder="请输入姓" required>
                        <i class="bi bi-person input-icon"></i>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="lastName">
                        <i class="bi bi-person"></i> 名
                    </label>
                    <div style="position: relative;">
                        <input type="text" id="lastName" class="form-input" placeholder="请输入名" required>
                        <i class="bi bi-person input-icon"></i>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="username">
                    <i class="bi bi-at"></i> 用户名
                </label>
                <div style="position: relative;">
                    <input type="text" id="username" class="form-input" placeholder="请输入用户名" required>
                    <i class="bi bi-at input-icon"></i>
                </div>
                <div class="form-help">用户名长度为3-20个字符，只能包含字母、数字和下划线</div>
            </div>

            <div class="form-group">
                <label class="form-label" for="email">
                    <i class="bi bi-envelope"></i> 邮箱地址
                </label>
                <div style="position: relative;">
                    <input type="email" id="email" class="form-input" placeholder="请输入邮箱地址" required>
                    <i class="bi bi-envelope input-icon"></i>
                </div>
                <div class="form-help">用于登录和接收重要通知</div>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">
                    <i class="bi bi-lock"></i> 密码
                </label>
                <div style="position: relative;">
                    <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                    <i class="bi bi-eye password-toggle" id="passwordToggle"></i>
                </div>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <span id="strengthText">请输入密码</span>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="confirmPassword">
                    <i class="bi bi-lock-fill"></i> 确认密码
                </label>
                <div style="position: relative;">
                    <input type="password" id="confirmPassword" class="form-input" placeholder="请再次输入密码" required>
                    <i class="bi bi-eye password-toggle" id="confirmPasswordToggle"></i>
                </div>
            </div>

            <div class="terms-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="agreeTerms" class="checkbox" required>
                    <label for="agreeTerms" class="checkbox-label">
                        我已阅读并同意 <a href="#" target="_blank">服务条款</a> 和 <a href="#" target="_blank">隐私政策</a>
                    </label>
                </div>
            </div>

            <button type="submit" class="register-btn" id="registerBtn">
                <span class="btn-text">创建账户</span>
                <div class="btn-loading">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>

        <div class="login-link">
            <p>已有账户？ <a href="new-login.html" id="loginLink">立即登录</a></p>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        function setupPasswordToggle(inputId, toggleId) {
            const passwordInput = document.getElementById(inputId);
            const passwordToggle = document.getElementById(toggleId);

            passwordToggle.addEventListener('click', function() {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    passwordToggle.className = 'bi bi-eye-slash password-toggle';
                } else {
                    passwordInput.type = 'password';
                    passwordToggle.className = 'bi bi-eye password-toggle';
                }
            });
        }

        setupPasswordToggle('password', 'passwordToggle');
        setupPasswordToggle('confirmPassword', 'confirmPasswordToggle');

        // 密码强度检测
        function checkPasswordStrength(password) {
            let strength = 0;
            let text = '';
            let className = '';

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                    text = '密码强度：弱';
                    className = 'strength-weak';
                    break;
                case 2:
                    text = '密码强度：一般';
                    className = 'strength-fair';
                    break;
                case 3:
                case 4:
                    text = '密码强度：良好';
                    className = 'strength-good';
                    break;
                case 5:
                    text = '密码强度：强';
                    className = 'strength-strong';
                    break;
            }

            return { text, className };
        }

        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            if (password.length === 0) {
                strengthFill.className = 'strength-fill';
                strengthText.textContent = '请输入密码';
                return;
            }

            const result = checkPasswordStrength(password);
            strengthFill.className = `strength-fill ${result.className}`;
            strengthText.textContent = result.text;
        });

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = `alert-${type}`;
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    ${message}
                </div>
            `;

            // 5秒后自动隐藏
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        // 设置按钮加载状态
        function setLoading(loading) {
            const registerBtn = document.getElementById('registerBtn');
            const btnText = registerBtn.querySelector('.btn-text');
            const btnLoading = registerBtn.querySelector('.btn-loading');

            if (loading) {
                registerBtn.disabled = true;
                btnText.style.opacity = '0';
                btnLoading.style.display = 'block';
            } else {
                registerBtn.disabled = false;
                btnText.style.opacity = '1';
                btnLoading.style.display = 'none';
            }
        }

        // 表单验证
        function validateForm() {
            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const agreeTerms = document.getElementById('agreeTerms').checked;

            if (!firstName || !lastName) {
                showAlert('请填写完整的姓名', 'error');
                return false;
            }

            if (username.length < 3 || username.length > 20) {
                showAlert('用户名长度必须在3-20个字符之间', 'error');
                return false;
            }

            if (!/^[a-zA-Z0-9_]+$/.test(username)) {
                showAlert('用户名只能包含字母、数字和下划线', 'error');
                return false;
            }

            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                showAlert('请输入有效的邮箱地址', 'error');
                return false;
            }

            if (password.length < 6) {
                showAlert('密码长度至少为6个字符', 'error');
                return false;
            }

            if (password !== confirmPassword) {
                showAlert('两次输入的密码不一致', 'error');
                return false;
            }

            if (!agreeTerms) {
                showAlert('请同意服务条款和隐私政策', 'error');
                return false;
            }

            return true;
        }

        // 注册表单提交
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }

            const firstName = document.getElementById('firstName').value.trim();
            const lastName = document.getElementById('lastName').value.trim();
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            const fullName = `${firstName} ${lastName}`;

            setLoading(true);

            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, email, password, fullName })
                });

                const data = await response.json();

                if (response.ok) {
                    showAlert('注册成功！正在跳转到登录页面...', 'success');
                    
                    // 延迟跳转到登录页面
                    setTimeout(() => {
                        window.location.href = 'new-login.html';
                    }, 2000);
                } else {
                    showAlert(data.error || '注册失败', 'error');
                }
            } catch (error) {
                console.error('注册错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                setLoading(false);
            }
        });
    </script>
</body>
</html>
