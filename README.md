# 智能日历系统 - MySQL版

基于Node.js + MySQL + Bootstrap 5的现代化智能日历系统，支持3×4布局年历、备忘录管理、天气预报等功能。

## ✨ 功能特性

### 📅 核心功能
- **3×4布局年历** - 12个月份网格显示，一目了然
- **单击月份查看详情** - 点击月份卡片打开详细页面
- **单击日期添加备忘录** - 直接点击日期快速添加备忘录
- **天气预报集成** - 实时天气信息和未来预报

### 🗓️ 日历功能
- **农历显示** - 每日显示对应农历日期
- **干支纪年** - 完整的天干地支年份显示
- **节假日标记** - 法定假日、传统节日、二十四节气
- **颜色区分** - 不同类型节假日用不同颜色标识

### 📝 备忘录管理
- **完整CRUD** - 创建、查看、编辑、删除备忘录
- **分类管理** - 工作、个人、家庭、健康等分类
- **优先级设置** - 高、中、低三级优先级
- **状态跟踪** - 待办、已完成、已取消状态
- **搜索功能** - 支持标题和内容搜索
- **统计分析** - 多维度数据统计

### 👤 用户系统
- **用户注册登录** - 完整的用户认证系统
- **个人资料管理** - 用户信息和偏好设置
- **权限控制** - 基于JWT的安全认证
- **数据隔离** - 用户数据完全隔离

### 🎨 界面设计
- **现代化UI** - Bootstrap 5 + 自定义样式
- **响应式设计** - 完美适配各种设备
- **渐变背景** - 多种渐变色彩营造现代感
- **毛玻璃效果** - backdrop-filter增强层次感
- **交互动画** - 丰富的悬停和点击动画

## 🛠️ 技术栈

### 后端技术
- **Node.js** - 服务器运行环境
- **Express.js** - Web应用框架
- **MySQL** - 关系型数据库
- **JWT** - 用户认证
- **bcryptjs** - 密码加密

### 前端技术
- **Bootstrap 5** - UI框架
- **Bootstrap Icons** - 图标库
- **原生JavaScript** - 前端逻辑
- **CSS3** - 样式和动画

### 开发工具
- **nodemon** - 开发时自动重启
- **dotenv** - 环境变量管理

## 📦 安装部署

### 环境要求
- Node.js 14.0+
- MySQL 5.7+
- 现代浏览器

### 快速安装

1. **克隆项目**
```bash
git clone <repository-url>
cd smart-calendar-mysql
```

2. **运行安装脚本**
```bash
# Windows
install.bat

# Linux/Mac
chmod +x install.sh
./install.sh
```

3. **手动安装（可选）**
```bash
# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接

# 初始化数据库
npm run init-db

# 启动服务器
npm start
```

### 配置说明

编辑 `.env` 文件配置数据库连接：

```env
# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=521223
DB_NAME=smart_calendar

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT密钥
JWT_SECRET=your_jwt_secret_key
```

## 🚀 使用说明

### 启动服务器
```bash
npm start
# 或者运行 start.bat (Windows)
```

### 访问系统
- 浏览器访问：http://localhost:3000
- 默认管理员账户：
  - 用户名：`admin`
  - 密码：`123456`

### 基本操作

1. **用户注册/登录**
   - 首次访问需要注册账户
   - 使用用户名/邮箱和密码登录

2. **浏览年历**
   - 查看12个月份的整体布局
   - 点击左右箭头切换年份

3. **查看月份详情**
   - 点击月份卡片打开详细页面
   - 支持打印功能

4. **添加备忘录**
   - 点击日期直接添加备忘录
   - 设置标题、内容、优先级、分类

5. **查看天气**
   - 点击天气图标查看天气预报
   - 支持拖拽移动窗口位置

## 📊 数据库结构

### 主要数据表

- **users** - 用户信息表
- **memos** - 备忘录表
- **holidays** - 节假日表
- **user_settings** - 用户设置表
- **system_logs** - 系统日志表

### 数据关系
- 用户与备忘录：一对多关系
- 用户与设置：一对多关系
- 支持级联删除和外键约束

## 🔧 API接口

### 认证接口
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `PUT /api/auth/profile` - 更新用户资料

### 备忘录接口
- `GET /api/memos` - 获取备忘录列表
- `POST /api/memos` - 创建备忘录
- `PUT /api/memos/:id` - 更新备忘录
- `DELETE /api/memos/:id` - 删除备忘录
- `GET /api/memos/stats/summary` - 获取统计信息

### 节假日接口
- `GET /api/holidays` - 获取节假日列表
- `GET /api/holidays/year/:year` - 获取指定年份节假日
- `GET /api/holidays/date/:date` - 获取指定日期节假日

### 设置接口
- `GET /api/settings` - 获取用户设置
- `PUT /api/settings/:key` - 设置配置项
- `DELETE /api/settings/:key` - 删除设置项

## 🎯 功能建议

基于当前系统，建议添加以下功能：

### 📱 移动端优化
- **PWA支持** - 离线使用和桌面安装
- **触摸手势** - 滑动切换月份/年份
- **推送通知** - 备忘录提醒通知

### 🔄 数据同步
- **云端同步** - 多设备数据同步
- **导入导出** - 支持多种格式导入导出
- **备份恢复** - 自动备份和一键恢复

### 🎨 个性化定制
- **主题切换** - 多种主题色彩选择
- **布局自定义** - 可调整的日历布局
- **字体设置** - 字体大小和样式选择

### 📊 高级功能
- **数据分析** - 备忘录完成率分析
- **习惯追踪** - 日常习惯记录和统计
- **团队协作** - 共享日历和团队备忘录

### 🔗 第三方集成
- **邮件提醒** - 邮件通知功能
- **日历同步** - 与Google Calendar等同步
- **天气API** - 接入真实天气API

### 🛡️ 安全增强
- **两步验证** - 增强账户安全
- **操作日志** - 详细的操作记录
- **数据加密** - 敏感数据加密存储

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📞 支持

如有问题，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 在线文档

---

**智能日历系统 - 让时间管理更智能！** 🎉
