const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * 数据库配置
 */
const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '521223',
  database: process.env.DB_NAME || 'smart_calendar',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  charset: 'utf8mb4',
  timezone: '+08:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

/**
 * 创建数据库连接池
 */
const pool = mysql.createPool(dbConfig);

/**
 * 数据库操作类
 */
class Database {
  constructor() {
    this.pool = pool;
  }

  /**
   * 执行查询
   * @param {string} sql SQL语句
   * @param {Array} params 参数
   * @returns {Promise<Array>} 查询结果
   */
  async query(sql, params = []) {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows;
    } catch (error) {
      console.error('数据库查询错误:', error);
      throw error;
    }
  }

  /**
   * 执行事务
   * @param {Function} callback 事务回调函数
   * @returns {Promise<any>} 事务结果
   */
  async transaction(callback) {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      
      const result = await callback(connection);
      
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * 测试数据库连接
   * @returns {Promise<boolean>} 连接状态
   */
  async testConnection() {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      console.error('数据库连接测试失败:', error);
      return false;
    }
  }

  /**
   * 获取数据库统计信息
   * @returns {Promise<Object>} 统计信息
   */
  async getStats() {
    try {
      const [stats] = await this.pool.execute(`
        SELECT 
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM memos) as total_memos,
          (SELECT COUNT(*) FROM holidays) as total_holidays,
          (SELECT COUNT(*) FROM user_settings) as total_settings
      `);
      
      return stats[0];
    } catch (error) {
      console.error('获取数据库统计信息失败:', error);
      return {
        total_users: 0,
        total_memos: 0,
        total_holidays: 0,
        total_settings: 0
      };
    }
  }

  /**
   * 关闭数据库连接池
   */
  async close() {
    try {
      await this.pool.end();
      console.log('数据库连接池已关闭');
    } catch (error) {
      console.error('关闭数据库连接池失败:', error);
    }
  }

  /**
   * 获取连接池状态
   * @returns {Object} 连接池状态
   */
  getPoolStatus() {
    return {
      totalConnections: this.pool._allConnections.length,
      freeConnections: this.pool._freeConnections.length,
      acquiringConnections: this.pool._acquiringConnections.length,
      connectionLimit: this.pool.config.connectionLimit
    };
  }
}

// 创建数据库实例
const db = new Database();

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('正在关闭数据库连接...');
  await db.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('正在关闭数据库连接...');
  await db.close();
  process.exit(0);
});

module.exports = db;
