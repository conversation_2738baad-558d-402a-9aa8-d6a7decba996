/**
 * API 客户端模块
 */
class ApiClient {
  constructor() {
    this.baseURL = '/api';
    this.token = localStorage.getItem('auth_token');
  }

  /**
   * 设置认证令牌
   */
  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  /**
   * 获取认证令牌
   */
  getToken() {
    return this.token || localStorage.getItem('auth_token');
  }

  /**
   * 获取请求头
   */
  getHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    const token = this.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * 发送HTTP请求
   */
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API请求失败:', error);
      throw error;
    }
  }

  /**
   * GET 请求
   */
  async get(endpoint, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;
    
    return this.request(url, {
      method: 'GET'
    });
  }

  /**
   * POST 请求
   */
  async post(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * PUT 请求
   */
  async put(endpoint, data = {}) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  /**
   * DELETE 请求
   */
  async delete(endpoint) {
    return this.request(endpoint, {
      method: 'DELETE'
    });
  }

  // 认证相关API
  async login(username, password) {
    return this.post('/auth/login', { username, password });
  }

  async register(username, email, password, fullName) {
    return this.post('/auth/register', { username, email, password, fullName });
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async getCurrentUser() {
    return this.get('/auth/me');
  }

  async updateProfile(data) {
    return this.put('/auth/profile', data);
  }

  async changePassword(currentPassword, newPassword) {
    return this.put('/auth/password', { currentPassword, newPassword });
  }

  // 备忘录相关API
  async getMemos(params = {}) {
    return this.get('/memos', params);
  }

  async getMemosByDate(date) {
    return this.get(`/memos/date/${date}`);
  }

  async getMemo(id) {
    return this.get(`/memos/${id}`);
  }

  async createMemo(data) {
    return this.post('/memos', data);
  }

  async updateMemo(id, data) {
    return this.put(`/memos/${id}`, data);
  }

  async deleteMemo(id) {
    return this.delete(`/memos/${id}`);
  }

  async deleteMemos(ids) {
    return this.delete('/memos', { ids });
  }

  async getMemoStats(year) {
    return this.get('/memos/stats/summary', { year });
  }

  // 节假日相关API
  async getHolidays(params = {}) {
    return this.get('/holidays', params);
  }

  async getHolidaysByYear(year) {
    return this.get(`/holidays/year/${year}`);
  }

  async getHolidaysByDate(date) {
    return this.get(`/holidays/date/${date}`);
  }

  async getHolidayStats(year) {
    return this.get('/holidays/stats/types', { year });
  }

  // 用户设置相关API
  async getSettings() {
    return this.get('/settings');
  }

  async getSetting(key) {
    return this.get(`/settings/${key}`);
  }

  async setSetting(key, value) {
    return this.put(`/settings/${key}`, { value });
  }

  async setSettings(settings) {
    return this.put('/settings', { settings });
  }

  async deleteSetting(key) {
    return this.delete(`/settings/${key}`);
  }

  async resetSettings() {
    return this.delete('/settings');
  }

  async exportSettings() {
    return this.get('/settings/export/all');
  }

  async importSettings(settings, overwrite = false) {
    return this.post('/settings/import', { settings, overwrite });
  }

  async getDefaultSettings() {
    return this.get('/settings/templates/default');
  }

  // 系统相关API
  async getHealth() {
    return this.get('/health');
  }

  async getInfo() {
    return this.get('/info');
  }
}

// 创建全局API客户端实例
window.api = new ApiClient();

/**
 * API错误处理器
 */
class ApiErrorHandler {
  static handle(error) {
    console.error('API错误:', error);

    // 根据错误类型显示不同的提示
    if (error.message.includes('401') || error.message.includes('403')) {
      // 认证失败，跳转到登录页
      window.auth?.logout();
      return;
    }

    if (error.message.includes('网络')) {
      this.showError('网络连接失败，请检查网络设置');
      return;
    }

    if (error.message.includes('500')) {
      this.showError('服务器内部错误，请稍后重试');
      return;
    }

    // 显示具体错误信息
    this.showError(error.message || '未知错误');
  }

  static showError(message) {
    // 创建错误提示
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      <i class="bi bi-exclamation-triangle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 自动移除
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 5000);
  }

  static showSuccess(message) {
    // 创建成功提示
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      <i class="bi bi-check-circle me-2"></i>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 自动移除
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 3000);
  }
}

// 全局错误处理器
window.ApiErrorHandler = ApiErrorHandler;
