const express = require('express');
const db = require('../config/database');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

/**
 * 获取用户的所有备忘录
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { year, month, date, category, priority, status, search, page = 1, limit = 100 } = req.query;
    
    let sql = 'SELECT * FROM memos WHERE user_id = ?';
    let params = [userId];
    
    // 添加筛选条件
    if (year) {
      sql += ' AND YEAR(memo_date) = ?';
      params.push(year);
    }
    
    if (month) {
      sql += ' AND MONTH(memo_date) = ?';
      params.push(month);
    }
    
    if (date) {
      sql += ' AND memo_date = ?';
      params.push(date);
    }
    
    if (category) {
      sql += ' AND category = ?';
      params.push(category);
    }
    
    if (priority) {
      sql += ' AND priority = ?';
      params.push(priority);
    }
    
    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }
    
    if (search) {
      sql += ' AND (title LIKE ? OR content LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }
    
    // 排序
    sql += ' ORDER BY memo_date DESC, created_at DESC';
    
    // 分页
    const offset = (parseInt(page) - 1) * parseInt(limit);
    sql += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));
    
    const memos = await db.query(sql, params);
    
    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM memos WHERE user_id = ?';
    let countParams = [userId];
    
    if (year) {
      countSql += ' AND YEAR(memo_date) = ?';
      countParams.push(year);
    }
    
    if (month) {
      countSql += ' AND MONTH(memo_date) = ?';
      countParams.push(month);
    }
    
    if (date) {
      countSql += ' AND memo_date = ?';
      countParams.push(date);
    }
    
    if (category) {
      countSql += ' AND category = ?';
      countParams.push(category);
    }
    
    if (priority) {
      countSql += ' AND priority = ?';
      countParams.push(priority);
    }
    
    if (status) {
      countSql += ' AND status = ?';
      countParams.push(status);
    }
    
    if (search) {
      countSql += ' AND (title LIKE ? OR content LIKE ?)';
      countParams.push(`%${search}%`, `%${search}%`);
    }
    
    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;
    
    res.json({
      memos,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('获取备忘录失败:', error);
    res.status(500).json({
      error: '获取备忘录失败'
    });
  }
});

/**
 * 获取指定日期的备忘录
 */
router.get('/date/:date', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { date } = req.params;
    
    const memos = await db.query(
      'SELECT * FROM memos WHERE user_id = ? AND memo_date = ? ORDER BY start_time ASC, created_at ASC',
      [userId, date]
    );
    
    res.json({
      date,
      memos
    });
    
  } catch (error) {
    console.error('获取指定日期备忘录失败:', error);
    res.status(500).json({
      error: '获取指定日期备忘录失败'
    });
  }
});

/**
 * 获取单个备忘录
 */
router.get('/:id', checkResourceOwnership('memo'), async (req, res) => {
  try {
    const { id } = req.params;
    
    const memos = await db.query(
      'SELECT * FROM memos WHERE id = ?',
      [id]
    );
    
    if (memos.length === 0) {
      return res.status(404).json({
        error: '备忘录不存在'
      });
    }
    
    res.json({
      memo: memos[0]
    });
    
  } catch (error) {
    console.error('获取备忘录失败:', error);
    res.status(500).json({
      error: '获取备忘录失败'
    });
  }
});

/**
 * 创建新备忘录
 */
router.post('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    const {
      title,
      content,
      memoDate,
      priority = 'medium',
      category = 'other',
      status = 'pending',
      reminderTime,
      isAllDay = true,
      startTime,
      endTime,
      color = '#007bff',
      tags
    } = req.body;
    
    // 验证必填字段
    if (!title || !memoDate) {
      return res.status(400).json({
        error: '标题和日期不能为空'
      });
    }
    
    const result = await db.query(
      `INSERT INTO memos (
        user_id, title, content, memo_date, priority, category, status,
        reminder_time, is_all_day, start_time, end_time, color, tags
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, title, content, memoDate, priority, category, status,
        reminderTime, isAllDay, startTime, endTime, color,
        tags ? JSON.stringify(tags) : null
      ]
    );
    
    const memoId = result.insertId;
    
    // 获取创建的备忘录
    const newMemo = await db.query(
      'SELECT * FROM memos WHERE id = ?',
      [memoId]
    );
    
    res.status(201).json({
      message: '备忘录创建成功',
      memo: newMemo[0]
    });
    
  } catch (error) {
    console.error('创建备忘录失败:', error);
    res.status(500).json({
      error: '创建备忘录失败'
    });
  }
});

/**
 * 更新备忘录
 */
router.put('/:id', checkResourceOwnership('memo'), async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      memoDate,
      priority,
      category,
      status,
      reminderTime,
      isAllDay,
      startTime,
      endTime,
      color,
      tags
    } = req.body;
    
    await db.query(
      `UPDATE memos SET 
        title = ?, content = ?, memo_date = ?, priority = ?, category = ?, status = ?,
        reminder_time = ?, is_all_day = ?, start_time = ?, end_time = ?, color = ?, tags = ?
      WHERE id = ?`,
      [
        title, content, memoDate, priority, category, status,
        reminderTime, isAllDay, startTime, endTime, color,
        tags ? JSON.stringify(tags) : null, id
      ]
    );
    
    // 获取更新后的备忘录
    const updatedMemo = await db.query(
      'SELECT * FROM memos WHERE id = ?',
      [id]
    );
    
    res.json({
      message: '备忘录更新成功',
      memo: updatedMemo[0]
    });
    
  } catch (error) {
    console.error('更新备忘录失败:', error);
    res.status(500).json({
      error: '更新备忘录失败'
    });
  }
});

/**
 * 删除备忘录
 */
router.delete('/:id', checkResourceOwnership('memo'), async (req, res) => {
  try {
    const { id } = req.params;
    
    await db.query('DELETE FROM memos WHERE id = ?', [id]);
    
    res.json({
      message: '备忘录删除成功'
    });
    
  } catch (error) {
    console.error('删除备忘录失败:', error);
    res.status(500).json({
      error: '删除备忘录失败'
    });
  }
});

/**
 * 批量删除备忘录
 */
router.delete('/', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        error: '请提供要删除的备忘录ID列表'
      });
    }
    
    // 验证所有备忘录都属于当前用户
    const placeholders = ids.map(() => '?').join(',');
    const memos = await db.query(
      `SELECT id FROM memos WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, userId]
    );
    
    if (memos.length !== ids.length) {
      return res.status(403).json({
        error: '部分备忘录不存在或无权删除'
      });
    }
    
    // 批量删除
    await db.query(
      `DELETE FROM memos WHERE id IN (${placeholders}) AND user_id = ?`,
      [...ids, userId]
    );
    
    res.json({
      message: `成功删除 ${ids.length} 条备忘录`
    });
    
  } catch (error) {
    console.error('批量删除备忘录失败:', error);
    res.status(500).json({
      error: '批量删除备忘录失败'
    });
  }
});

/**
 * 获取备忘录统计信息
 */
router.get('/stats/summary', async (req, res) => {
  try {
    const userId = req.user.userId;
    const { year = new Date().getFullYear() } = req.query;
    
    // 总数统计
    const totalResult = await db.query(
      'SELECT COUNT(*) as total FROM memos WHERE user_id = ? AND YEAR(memo_date) = ?',
      [userId, year]
    );
    
    // 按优先级统计
    const priorityStats = await db.query(
      'SELECT priority, COUNT(*) as count FROM memos WHERE user_id = ? AND YEAR(memo_date) = ? GROUP BY priority',
      [userId, year]
    );
    
    // 按分类统计
    const categoryStats = await db.query(
      'SELECT category, COUNT(*) as count FROM memos WHERE user_id = ? AND YEAR(memo_date) = ? GROUP BY category',
      [userId, year]
    );
    
    // 按状态统计
    const statusStats = await db.query(
      'SELECT status, COUNT(*) as count FROM memos WHERE user_id = ? AND YEAR(memo_date) = ? GROUP BY status',
      [userId, year]
    );
    
    // 按月份统计
    const monthlyStats = await db.query(
      'SELECT MONTH(memo_date) as month, COUNT(*) as count FROM memos WHERE user_id = ? AND YEAR(memo_date) = ? GROUP BY MONTH(memo_date) ORDER BY month',
      [userId, year]
    );
    
    res.json({
      year: parseInt(year),
      total: totalResult[0].total,
      priority: priorityStats.reduce((acc, item) => {
        acc[item.priority] = item.count;
        return acc;
      }, {}),
      category: categoryStats.reduce((acc, item) => {
        acc[item.category] = item.count;
        return acc;
      }, {}),
      status: statusStats.reduce((acc, item) => {
        acc[item.status] = item.count;
        return acc;
      }, {}),
      monthly: monthlyStats.reduce((acc, item) => {
        acc[item.month] = item.count;
        return acc;
      }, {})
    });
    
  } catch (error) {
    console.error('获取备忘录统计失败:', error);
    res.status(500).json({
      error: '获取备忘录统计失败'
    });
  }
});

module.exports = router;
