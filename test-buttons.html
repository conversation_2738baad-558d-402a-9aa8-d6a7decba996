<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>按钮功能测试</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>测试按钮</h3>
                <button type="button" class="btn btn-success me-2" onclick="testAddMemo()">
                    <i class="bi bi-plus-circle me-1"></i>测试添加备忘录
                </button>
                
                <button type="button" class="btn btn-primary me-2" onclick="testToday()">
                    <i class="bi bi-calendar-check me-1"></i>测试今天按钮
                </button>
                
                <button type="button" class="btn btn-info me-2" onclick="testMemoList()">
                    <i class="bi bi-list me-1"></i>测试备忘录列表
                </button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>测试结果</h3>
                <div id="testResults" class="alert alert-info">
                    点击上面的按钮进行测试...
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>控制台日志</h3>
                <div id="consoleLog" class="bg-dark text-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace;">
                    等待日志输出...
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 重写console.log来显示在页面上
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('consoleLog');
        
        function addLogToPage(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'warn' ? 'text-warning' : 'text-light';
            logContainer.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogToPage(args.join(' '), 'error');
        };
        
        // 测试函数
        function testAddMemo() {
            console.log('🎯 测试添加备忘录按钮');
            updateResults('测试添加备忘录按钮 - 点击成功！');
            
            // 模拟打开备忘录模态框
            setTimeout(() => {
                console.log('✅ 模拟打开备忘录模态框');
                updateResults('备忘录模态框已打开（模拟）');
            }, 500);
        }
        
        function testToday() {
            console.log('🎯 测试今天按钮');
            updateResults('测试今天按钮 - 点击成功！');
            
            const today = new Date();
            console.log(`📅 今天是: ${today.toLocaleDateString()}`);
            updateResults(`今天是: ${today.toLocaleDateString()}`);
        }
        
        function testMemoList() {
            console.log('🎯 测试备忘录列表按钮');
            updateResults('测试备忘录列表按钮 - 点击成功！');
            
            // 模拟打开备忘录列表
            setTimeout(() => {
                console.log('✅ 模拟打开备忘录列表');
                updateResults('备忘录列表已打开（模拟）');
            }, 500);
        }
        
        function updateResults(message) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `<strong>最新结果:</strong> ${message}`;
            resultsDiv.className = 'alert alert-success';
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ 测试页面加载完成');
            console.log('🔧 所有按钮事件已绑定');
            updateResults('页面加载完成，可以开始测试');
        });
        
        // 检查主页面的函数是否存在
        setTimeout(() => {
            console.log('🔍 检查主页面函数...');
            
            if (typeof handleAddMemoClick === 'function') {
                console.log('✅ handleAddMemoClick 函数存在');
            } else {
                console.log('❌ handleAddMemoClick 函数不存在');
            }
            
            if (typeof handleTodayClick === 'function') {
                console.log('✅ handleTodayClick 函数存在');
            } else {
                console.log('❌ handleTodayClick 函数不存在');
            }
            
            if (window.memoManager) {
                console.log('✅ window.memoManager 存在');
            } else {
                console.log('❌ window.memoManager 不存在');
            }
        }, 1000);
    </script>
</body>
</html>
