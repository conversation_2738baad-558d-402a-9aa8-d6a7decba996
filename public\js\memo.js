/**
 * 备忘录管理模块
 */
class MemoManager {
  constructor() {
    this.modal = null;
    this.currentDate = null;
    this.editingMemo = null;
    
    this.init();
  }

  /**
   * 初始化备忘录管理器
   */
  init() {
    this.modal = new bootstrap.Modal(document.getElementById('memoModal'));
    this.bindEvents();
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 添加备忘录按钮
    document.getElementById('addMemoBtn').addEventListener('click', () => {
      this.openModal();
    });

    // 保存备忘录
    document.getElementById('saveMemo').addEventListener('click', () => {
      this.saveMemo();
    });

    // 模态框关闭时重置表单
    document.getElementById('memoModal').addEventListener('hidden.bs.modal', () => {
      this.resetForm();
    });

    // 表单验证
    document.getElementById('memoForm').addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveMemo();
    });
  }

  /**
   * 打开备忘录模态框
   */
  openModal(date = null) {
    this.currentDate = date || new Date().toISOString().split('T')[0];
    this.editingMemo = null;
    
    // 设置默认日期
    document.getElementById('memoDate').value = this.currentDate;
    
    // 设置模态框标题
    const title = date ? `添加备忘录 - ${this.formatDate(date)}` : '添加备忘录';
    document.getElementById('memoModalTitle').textContent = title;
    
    // 显示模态框
    this.modal.show();
    
    // 聚焦到标题输入框
    setTimeout(() => {
      document.getElementById('memoTitle').focus();
    }, 300);
  }

  /**
   * 保存备忘录
   */
  async saveMemo() {
    const form = document.getElementById('memoForm');
    
    // 验证表单
    if (!form.checkValidity()) {
      form.classList.add('was-validated');
      return;
    }
    
    // 获取表单数据
    const memoData = {
      memoDate: document.getElementById('memoDate').value,
      title: document.getElementById('memoTitle').value.trim(),
      content: document.getElementById('memoContent').value.trim(),
      priority: document.getElementById('memoPriority').value,
      category: document.getElementById('memoCategory').value
    };
    
    // 验证必填字段
    if (!memoData.title) {
      this.showAlert('请输入备忘录标题', 'warning');
      return;
    }
    
    try {
      if (this.editingMemo) {
        // 编辑现有备忘录
        await window.api.updateMemo(this.editingMemo.id, memoData);
        this.showAlert('备忘录更新成功！', 'success');
      } else {
        // 添加新备忘录
        await window.api.createMemo(memoData);
        this.showAlert('备忘录添加成功！', 'success');
      }
      
      // 关闭模态框
      this.modal.hide();
      
      // 重新加载日历数据
      if (window.calendar) {
        await window.calendar.loadData();
      }
      
    } catch (error) {
      console.error('保存备忘录失败:', error);
      this.showAlert('保存失败，请重试', 'danger');
    }
  }

  /**
   * 编辑备忘录
   */
  editMemo(memo) {
    this.currentDate = memo.memo_date;
    this.editingMemo = memo;
    
    // 填充表单
    document.getElementById('memoDate').value = memo.memo_date;
    document.getElementById('memoTitle').value = memo.title;
    document.getElementById('memoContent').value = memo.content || '';
    document.getElementById('memoPriority').value = memo.priority || 'medium';
    document.getElementById('memoCategory').value = memo.category || 'other';
    
    // 设置模态框标题
    document.getElementById('memoModalTitle').textContent = `编辑备忘录 - ${this.formatDate(memo.memo_date)}`;
    
    // 显示模态框
    this.modal.show();
  }

  /**
   * 删除备忘录
   */
  async deleteMemo(memoId) {
    if (!confirm('确定要删除这个备忘录吗？')) {
      return;
    }
    
    try {
      await window.api.deleteMemo(memoId);
      this.showAlert('备忘录删除成功！', 'success');
      
      // 重新加载日历数据
      if (window.calendar) {
        await window.calendar.loadData();
      }
      
    } catch (error) {
      console.error('删除备忘录失败:', error);
      this.showAlert('删除失败，请重试', 'danger');
    }
  }

  /**
   * 获取指定日期的备忘录
   */
  async getMemos(date) {
    try {
      const response = await window.api.getMemosByDate(date);
      return response.memos || [];
    } catch (error) {
      console.error('获取备忘录失败:', error);
      return [];
    }
  }

  /**
   * 搜索备忘录
   */
  async searchMemos(keyword) {
    try {
      const response = await window.api.getMemos({ search: keyword });
      return response.memos || [];
    } catch (error) {
      console.error('搜索备忘录失败:', error);
      return [];
    }
  }

  /**
   * 重置表单
   */
  resetForm() {
    const form = document.getElementById('memoForm');
    form.reset();
    form.classList.remove('was-validated');
    
    this.currentDate = null;
    this.editingMemo = null;
    
    // 重置为默认值
    document.getElementById('memoPriority').value = 'medium';
    document.getElementById('memoCategory').value = 'other';
  }

  /**
   * 格式化日期显示
   */
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekday = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
    
    return `${year}年${month}月${day}日 星期${weekday}`;
  }

  /**
   * 显示提示信息
   */
  showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 自动移除
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 3000);
  }

  /**
   * 获取备忘录统计信息
   */
  async getStats(year) {
    try {
      const response = await window.api.getMemoStats(year);
      return response;
    } catch (error) {
      console.error('获取备忘录统计失败:', error);
      return {
        total: 0,
        priority: {},
        category: {},
        status: {},
        monthly: {}
      };
    }
  }

  /**
   * 导出备忘录数据
   */
  async exportMemos() {
    try {
      const response = await window.api.getMemos();
      const memos = response.memos || [];
      
      const dataStr = JSON.stringify(memos, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      
      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `memos_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      this.showAlert('备忘录导出成功！', 'success');
      
    } catch (error) {
      console.error('导出备忘录失败:', error);
      this.showAlert('导出失败，请重试', 'danger');
    }
  }

  /**
   * 导入备忘录数据
   */
  importMemos(file) {
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      try {
        const memos = JSON.parse(e.target.result);
        
        let successCount = 0;
        let errorCount = 0;
        
        for (const memo of memos) {
          try {
            await window.api.createMemo(memo);
            successCount++;
          } catch (error) {
            console.error('导入备忘录失败:', error);
            errorCount++;
          }
        }
        
        this.showAlert(`导入完成！成功：${successCount}，失败：${errorCount}`, 'info');
        
        // 重新加载日历数据
        if (window.calendar) {
          await window.calendar.loadData();
        }
        
      } catch (error) {
        console.error('解析文件失败:', error);
        this.showAlert('文件格式错误，请检查文件', 'danger');
      }
    };
    
    reader.readAsText(file);
  }
}

// 全局备忘录管理器实例
window.memoManager = null;
