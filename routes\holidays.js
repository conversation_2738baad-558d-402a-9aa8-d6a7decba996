const express = require('express');
const db = require('../config/database');
const { optionalAuth, requireAdmin } = require('../middleware/auth');

const router = express.Router();

/**
 * 获取节假日列表
 */
router.get('/', optionalAuth, async (req, res) => {
  try {
    const { year, month, type, page = 1, limit = 100 } = req.query;
    
    let sql = 'SELECT * FROM holidays WHERE 1=1';
    let params = [];
    
    // 添加筛选条件
    if (year) {
      sql += ' AND year = ?';
      params.push(year);
    }
    
    if (month) {
      sql += ' AND MONTH(holiday_date) = ?';
      params.push(month);
    }
    
    if (type) {
      sql += ' AND type = ?';
      params.push(type);
    }
    
    // 排序
    sql += ' ORDER BY holiday_date ASC';
    
    // 分页
    const offset = (page - 1) * limit;
    sql += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);
    
    const holidays = await db.query(sql, params);
    
    // 获取总数
    let countSql = 'SELECT COUNT(*) as total FROM holidays WHERE 1=1';
    let countParams = [];
    
    if (year) {
      countSql += ' AND year = ?';
      countParams.push(year);
    }
    
    if (month) {
      countSql += ' AND MONTH(holiday_date) = ?';
      countParams.push(month);
    }
    
    if (type) {
      countSql += ' AND type = ?';
      countParams.push(type);
    }
    
    const countResult = await db.query(countSql, countParams);
    const total = countResult[0].total;
    
    res.json({
      holidays,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error) {
    console.error('获取节假日失败:', error);
    res.status(500).json({
      error: '获取节假日失败'
    });
  }
});

/**
 * 获取指定年份的节假日
 */
router.get('/year/:year', optionalAuth, async (req, res) => {
  try {
    const { year } = req.params;
    
    const holidays = await db.query(
      'SELECT * FROM holidays WHERE year = ? ORDER BY holiday_date ASC',
      [year]
    );
    
    // 按类型分组
    const groupedHolidays = holidays.reduce((acc, holiday) => {
      if (!acc[holiday.type]) {
        acc[holiday.type] = [];
      }
      acc[holiday.type].push(holiday);
      return acc;
    }, {});
    
    res.json({
      year: parseInt(year),
      holidays: groupedHolidays,
      total: holidays.length
    });
    
  } catch (error) {
    console.error('获取年份节假日失败:', error);
    res.status(500).json({
      error: '获取年份节假日失败'
    });
  }
});

/**
 * 获取指定日期的节假日
 */
router.get('/date/:date', optionalAuth, async (req, res) => {
  try {
    const { date } = req.params;
    
    const holidays = await db.query(
      'SELECT * FROM holidays WHERE holiday_date = ?',
      [date]
    );
    
    res.json({
      date,
      holidays
    });
    
  } catch (error) {
    console.error('获取指定日期节假日失败:', error);
    res.status(500).json({
      error: '获取指定日期节假日失败'
    });
  }
});

/**
 * 获取节假日类型统计
 */
router.get('/stats/types', optionalAuth, async (req, res) => {
  try {
    const { year } = req.query;
    
    let sql = 'SELECT type, COUNT(*) as count FROM holidays';
    let params = [];
    
    if (year) {
      sql += ' WHERE year = ?';
      params.push(year);
    }
    
    sql += ' GROUP BY type';
    
    const stats = await db.query(sql, params);
    
    res.json({
      year: year ? parseInt(year) : null,
      types: stats.reduce((acc, item) => {
        acc[item.type] = item.count;
        return acc;
      }, {})
    });
    
  } catch (error) {
    console.error('获取节假日类型统计失败:', error);
    res.status(500).json({
      error: '获取节假日类型统计失败'
    });
  }
});

/**
 * 创建新节假日 (需要管理员权限)
 */
router.post('/', requireAdmin, async (req, res) => {
  try {
    const { name, holidayDate, type, description, isWorkingDay = false } = req.body;
    
    // 验证必填字段
    if (!name || !holidayDate || !type) {
      return res.status(400).json({
        error: '名称、日期和类型不能为空'
      });
    }
    
    // 验证类型
    const validTypes = ['legal', 'traditional', 'solar_term'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        error: '无效的节假日类型'
      });
    }
    
    // 提取年份
    const year = new Date(holidayDate).getFullYear();
    
    const result = await db.query(
      'INSERT INTO holidays (name, holiday_date, type, description, is_working_day, year) VALUES (?, ?, ?, ?, ?, ?)',
      [name, holidayDate, type, description, isWorkingDay, year]
    );
    
    const holidayId = result.insertId;
    
    // 获取创建的节假日
    const newHoliday = await db.query(
      'SELECT * FROM holidays WHERE id = ?',
      [holidayId]
    );
    
    res.status(201).json({
      message: '节假日创建成功',
      holiday: newHoliday[0]
    });
    
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        error: '该日期的节假日已存在'
      });
    }
    
    console.error('创建节假日失败:', error);
    res.status(500).json({
      error: '创建节假日失败'
    });
  }
});

/**
 * 更新节假日 (需要管理员权限)
 */
router.put('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, holidayDate, type, description, isWorkingDay } = req.body;
    
    // 检查节假日是否存在
    const existingHoliday = await db.query(
      'SELECT id FROM holidays WHERE id = ?',
      [id]
    );
    
    if (existingHoliday.length === 0) {
      return res.status(404).json({
        error: '节假日不存在'
      });
    }
    
    // 验证类型
    if (type) {
      const validTypes = ['legal', 'traditional', 'solar_term'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({
          error: '无效的节假日类型'
        });
      }
    }
    
    // 更新年份
    const year = holidayDate ? new Date(holidayDate).getFullYear() : null;
    
    await db.query(
      'UPDATE holidays SET name = ?, holiday_date = ?, type = ?, description = ?, is_working_day = ?, year = ? WHERE id = ?',
      [name, holidayDate, type, description, isWorkingDay, year, id]
    );
    
    // 获取更新后的节假日
    const updatedHoliday = await db.query(
      'SELECT * FROM holidays WHERE id = ?',
      [id]
    );
    
    res.json({
      message: '节假日更新成功',
      holiday: updatedHoliday[0]
    });
    
  } catch (error) {
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(409).json({
        error: '该日期的节假日已存在'
      });
    }
    
    console.error('更新节假日失败:', error);
    res.status(500).json({
      error: '更新节假日失败'
    });
  }
});

/**
 * 删除节假日 (需要管理员权限)
 */
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查节假日是否存在
    const existingHoliday = await db.query(
      'SELECT id FROM holidays WHERE id = ?',
      [id]
    );
    
    if (existingHoliday.length === 0) {
      return res.status(404).json({
        error: '节假日不存在'
      });
    }
    
    await db.query('DELETE FROM holidays WHERE id = ?', [id]);
    
    res.json({
      message: '节假日删除成功'
    });
    
  } catch (error) {
    console.error('删除节假日失败:', error);
    res.status(500).json({
      error: '删除节假日失败'
    });
  }
});

/**
 * 批量导入节假日 (需要管理员权限)
 */
router.post('/batch', requireAdmin, async (req, res) => {
  try {
    const { holidays } = req.body;
    
    if (!holidays || !Array.isArray(holidays) || holidays.length === 0) {
      return res.status(400).json({
        error: '请提供节假日数据'
      });
    }
    
    let successCount = 0;
    let errorCount = 0;
    const errors = [];
    
    for (const holiday of holidays) {
      try {
        const { name, holidayDate, type, description, isWorkingDay = false } = holiday;
        
        if (!name || !holidayDate || !type) {
          errors.push(`节假日 ${name || '未知'}: 缺少必填字段`);
          errorCount++;
          continue;
        }
        
        const year = new Date(holidayDate).getFullYear();
        
        await db.query(
          'INSERT IGNORE INTO holidays (name, holiday_date, type, description, is_working_day, year) VALUES (?, ?, ?, ?, ?, ?)',
          [name, holidayDate, type, description, isWorkingDay, year]
        );
        
        successCount++;
      } catch (error) {
        errors.push(`节假日 ${holiday.name || '未知'}: ${error.message}`);
        errorCount++;
      }
    }
    
    res.json({
      message: '批量导入完成',
      success: successCount,
      errors: errorCount,
      details: errors
    });
    
  } catch (error) {
    console.error('批量导入节假日失败:', error);
    res.status(500).json({
      error: '批量导入节假日失败'
    });
  }
});

module.exports = router;
