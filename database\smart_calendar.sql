-- 智能日历系统数据库
-- 创建时间: 2025-01-16
-- 版本: 1.0.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS smart_calendar 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE smart_calendar;

-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) COMMENT '姓名',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai' COMMENT '时区',
    language VARCHAR(10) DEFAULT 'zh-CN' COMMENT '语言',
    theme VARCHAR(20) DEFAULT 'default' COMMENT '主题',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 备忘录表
CREATE TABLE memos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    memo_date DATE NOT NULL COMMENT '备忘录日期',
    memo_time TIME COMMENT '备忘录时间',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium' COMMENT '优先级',
    category ENUM('work', 'personal', 'family', 'health', 'study', 'travel', 'other') DEFAULT 'other' COMMENT '分类',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    reminder_minutes INT COMMENT '提醒提前分钟数',
    tags JSON COMMENT '标签数组',
    is_recurring BOOLEAN DEFAULT FALSE COMMENT '是否重复',
    recurring_pattern VARCHAR(100) COMMENT '重复模式',
    parent_memo_id INT COMMENT '父备忘录ID（用于重复事件）',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_memo_id) REFERENCES memos(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, memo_date),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_category (category)
) COMMENT '备忘录表';

-- 节假日表
CREATE TABLE holidays (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '节假日名称',
    holiday_date DATE NOT NULL COMMENT '节假日日期',
    type ENUM('legal', 'traditional', 'solar_term', 'custom') NOT NULL COMMENT '类型',
    country VARCHAR(10) DEFAULT 'CN' COMMENT '国家代码',
    description TEXT COMMENT '描述',
    is_work_day BOOLEAN DEFAULT FALSE COMMENT '是否为调休工作日',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_date_type (holiday_date, type, name),
    INDEX idx_date (holiday_date),
    INDEX idx_type (type)
) COMMENT '节假日表';

-- 用户设置表
CREATE TABLE user_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '用户ID',
    setting_key VARCHAR(100) NOT NULL COMMENT '设置键',
    setting_value TEXT COMMENT '设置值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_user_setting (user_id, setting_key)
) COMMENT '用户设置表';

-- 备忘录附件表
CREATE TABLE memo_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    memo_id INT NOT NULL COMMENT '备忘录ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size INT COMMENT '文件大小（字节）',
    file_type VARCHAR(100) COMMENT '文件类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (memo_id) REFERENCES memos(id) ON DELETE CASCADE,
    INDEX idx_memo_id (memo_id)
) COMMENT '备忘录附件表';

-- 备忘录分享表
CREATE TABLE memo_shares (
    id INT PRIMARY KEY AUTO_INCREMENT,
    memo_id INT NOT NULL COMMENT '备忘录ID',
    shared_by INT NOT NULL COMMENT '分享者ID',
    shared_to INT NOT NULL COMMENT '被分享者ID',
    permission ENUM('read', 'write') DEFAULT 'read' COMMENT '权限',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (memo_id) REFERENCES memos(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_to) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY uk_memo_share (memo_id, shared_to)
) COMMENT '备忘录分享表';

-- 系统日志表
CREATE TABLE system_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id INT COMMENT '资源ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_action (user_id, action),
    INDEX idx_created_at (created_at)
) COMMENT '系统日志表';

-- 插入默认管理员用户
INSERT INTO users (username, email, password_hash, full_name, is_active) VALUES 
('admin', '<EMAIL>', '$2b$10$rQZ8kHWKQVnqVWKQVnqVWKQVnqVWKQVnqVWKQVnqVWKQVnqVWKQVnq', '系统管理员', TRUE);

-- 插入2025年中国法定节假日
INSERT INTO holidays (name, holiday_date, type, description) VALUES 
('元旦', '2025-01-01', 'legal', '新年第一天'),
('春节', '2025-01-29', 'legal', '农历新年'),
('春节', '2025-01-30', 'legal', '农历新年'),
('春节', '2025-01-31', 'legal', '农历新年'),
('春节', '2025-02-01', 'legal', '农历新年'),
('春节', '2025-02-02', 'legal', '农历新年'),
('春节', '2025-02-03', 'legal', '农历新年'),
('春节', '2025-02-04', 'legal', '农历新年'),
('清明节', '2025-04-05', 'legal', '祭祖扫墓的传统节日'),
('劳动节', '2025-05-01', 'legal', '国际劳动节'),
('劳动节', '2025-05-02', 'legal', '国际劳动节'),
('劳动节', '2025-05-03', 'legal', '国际劳动节'),
('端午节', '2025-05-31', 'legal', '纪念屈原的传统节日'),
('中秋节', '2025-10-06', 'legal', '团圆节'),
('国庆节', '2025-10-01', 'legal', '中华人民共和国成立纪念日'),
('国庆节', '2025-10-02', 'legal', '中华人民共和国成立纪念日'),
('国庆节', '2025-10-03', 'legal', '中华人民共和国成立纪念日'),
('国庆节', '2025-10-07', 'legal', '中华人民共和国成立纪念日');

-- 插入传统节日
INSERT INTO holidays (name, holiday_date, type, description) VALUES 
('情人节', '2025-02-14', 'traditional', '西方情人节'),
('妇女节', '2025-03-08', 'traditional', '国际妇女节'),
('植树节', '2025-03-12', 'traditional', '植树造林节'),
('愚人节', '2025-04-01', 'traditional', '愚人节'),
('青年节', '2025-05-04', 'traditional', '五四青年节'),
('儿童节', '2025-06-01', 'traditional', '国际儿童节'),
('建党节', '2025-07-01', 'traditional', '中国共产党成立纪念日'),
('建军节', '2025-08-01', 'traditional', '中国人民解放军建军节'),
('教师节', '2025-09-10', 'traditional', '尊师重教节'),
('圣诞节', '2025-12-25', 'traditional', '基督教节日');

-- 插入二十四节气
INSERT INTO holidays (name, holiday_date, type, description) VALUES 
('小寒', '2025-01-05', 'solar_term', '二十四节气之一'),
('大寒', '2025-01-20', 'solar_term', '二十四节气之一'),
('立春', '2025-02-04', 'solar_term', '二十四节气之一'),
('雨水', '2025-02-19', 'solar_term', '二十四节气之一'),
('惊蛰', '2025-03-06', 'solar_term', '二十四节气之一'),
('春分', '2025-03-21', 'solar_term', '二十四节气之一'),
('清明', '2025-04-05', 'solar_term', '二十四节气之一'),
('谷雨', '2025-04-20', 'solar_term', '二十四节气之一'),
('立夏', '2025-05-06', 'solar_term', '二十四节气之一'),
('小满', '2025-05-21', 'solar_term', '二十四节气之一'),
('芒种', '2025-06-06', 'solar_term', '二十四节气之一'),
('夏至', '2025-06-21', 'solar_term', '二十四节气之一'),
('小暑', '2025-07-07', 'solar_term', '二十四节气之一'),
('大暑', '2025-07-23', 'solar_term', '二十四节气之一'),
('立秋', '2025-08-08', 'solar_term', '二十四节气之一'),
('处暑', '2025-08-23', 'solar_term', '二十四节气之一'),
('白露', '2025-09-08', 'solar_term', '二十四节气之一'),
('秋分', '2025-09-23', 'solar_term', '二十四节气之一'),
('寒露', '2025-10-08', 'solar_term', '二十四节气之一'),
('霜降', '2025-10-23', 'solar_term', '二十四节气之一'),
('立冬', '2025-11-07', 'solar_term', '二十四节气之一'),
('小雪', '2025-11-22', 'solar_term', '二十四节气之一'),
('大雪', '2025-12-07', 'solar_term', '二十四节气之一'),
('冬至', '2025-12-22', 'solar_term', '二十四节气之一');

-- 创建视图：用户备忘录统计
CREATE VIEW user_memo_stats AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(m.id) as total_memos,
    COUNT(CASE WHEN m.status = 'pending' THEN 1 END) as pending_memos,
    COUNT(CASE WHEN m.status = 'completed' THEN 1 END) as completed_memos,
    COUNT(CASE WHEN m.priority = 'high' THEN 1 END) as high_priority_memos,
    COUNT(CASE WHEN m.memo_date = CURDATE() THEN 1 END) as today_memos
FROM users u
LEFT JOIN memos m ON u.id = m.user_id
GROUP BY u.id, u.username;

-- 创建存储过程：获取用户指定月份的备忘录
DELIMITER //
CREATE PROCEDURE GetUserMonthMemos(
    IN p_user_id INT,
    IN p_year INT,
    IN p_month INT
)
BEGIN
    SELECT 
        id,
        title,
        content,
        memo_date,
        memo_time,
        priority,
        category,
        status,
        tags,
        created_at
    FROM memos 
    WHERE user_id = p_user_id 
        AND YEAR(memo_date) = p_year 
        AND MONTH(memo_date) = p_month
    ORDER BY memo_date, memo_time;
END //
DELIMITER ;

-- 创建触发器：记录备忘录操作日志
DELIMITER //
CREATE TRIGGER memo_insert_log 
AFTER INSERT ON memos
FOR EACH ROW
BEGIN
    INSERT INTO system_logs (user_id, action, resource_type, resource_id)
    VALUES (NEW.user_id, 'CREATE', 'memo', NEW.id);
END //

CREATE TRIGGER memo_update_log 
AFTER UPDATE ON memos
FOR EACH ROW
BEGIN
    INSERT INTO system_logs (user_id, action, resource_type, resource_id)
    VALUES (NEW.user_id, 'UPDATE', 'memo', NEW.id);
END //

CREATE TRIGGER memo_delete_log 
AFTER DELETE ON memos
FOR EACH ROW
BEGIN
    INSERT INTO system_logs (user_id, action, resource_type, resource_id)
    VALUES (OLD.user_id, 'DELETE', 'memo', OLD.id);
END //
DELIMITER ;

-- 创建索引优化查询性能
CREATE INDEX idx_memos_user_date_status ON memos(user_id, memo_date, status);
CREATE INDEX idx_holidays_date_type ON holidays(holiday_date, type);
CREATE INDEX idx_users_username_active ON users(username, is_active);

-- 插入示例数据
INSERT INTO memos (user_id, title, content, memo_date, memo_time, priority, category, status, tags) VALUES 
(1, '团队会议', '讨论Q1季度计划和目标', '2025-01-20', '09:00:00', 'high', 'work', 'pending', '["会议", "计划", "重要"]'),
(1, '体检预约', '年度健康体检，记得空腹', '2025-01-25', '14:30:00', 'medium', 'health', 'pending', '["健康", "体检"]'),
(1, '情人节礼物', '准备情人节惊喜', '2025-02-14', NULL, 'medium', 'personal', 'pending', '["节日", "礼物"]'),
(1, '春节回家', '订购回家的火车票', '2025-01-29', NULL, 'high', 'family', 'pending', '["春节", "回家", "火车票"]'),
(1, '年终总结', '完成2024年工作总结报告', '2025-01-18', '16:00:00', 'high', 'work', 'in_progress', '["总结", "报告"]');

-- 设置字符集和排序规则
ALTER DATABASE smart_calendar CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
