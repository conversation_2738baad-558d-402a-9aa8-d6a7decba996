<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>智能日历系统 - 简化版</title>
  
  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
  <!-- 自定义样式 -->
  <link href="css/style.css" rel="stylesheet">
</head>
<body>
  <!-- 顶部导航栏 -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary fixed-top">
    <div class="container-fluid">
      <a class="navbar-brand fw-bold" href="#">
        <i class="bi bi-calendar-event me-2"></i>
        智能日历系统
      </a>
      
      <div class="navbar-nav ms-auto">
        <button class="btn btn-outline-light me-2" id="weatherBtn" title="天气预报">
          <i class="bi bi-cloud-sun"></i>
        </button>
        
        <div class="nav-item dropdown">
          <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
            <i class="bi bi-person-circle me-1"></i>
            <span id="userDisplayName">游客</span>
          </a>
          <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="alert('个人资料功能')">
              <i class="bi bi-person me-2"></i>个人资料
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="alert('设置功能')">
              <i class="bi bi-gear me-2"></i>设置
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item" href="#" onclick="alert('退出登录')">
              <i class="bi bi-box-arrow-right me-2"></i>退出登录
            </a></li>
          </ul>
        </div>
      </div>
    </div>
  </nav>

  <!-- 主内容区域 -->
  <main class="main-content">
    <!-- 年份控制区域 -->
    <div class="year-control-section">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-4">
            <div class="year-navigation">
              <button class="btn btn-primary btn-lg rounded-circle me-3" id="prevYear">
                <i class="bi bi-chevron-left"></i>
              </button>
              <div class="year-display">
                <h2 class="year-number mb-0" id="currentYear">2025</h2>
                <small class="chinese-year text-muted" id="chineseYear">乙巳蛇年</small>
              </div>
              <button class="btn btn-primary btn-lg rounded-circle ms-3" id="nextYear">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>
          </div>
          
          <div class="col-md-4 text-center">
            <div class="calendar-info">
              <h5 class="mb-1">3×4布局年历</h5>
              <p class="text-muted mb-0">智能日历系统</p>
            </div>
          </div>
          
          <div class="col-md-4 text-end">
            <div class="quick-actions">
              <button class="btn btn-outline-primary me-2" id="todayBtn">
                <i class="bi bi-calendar-check me-1"></i>今天
              </button>
              <button class="btn btn-success" id="addMemoBtn">
                <i class="bi bi-plus-circle me-1"></i>添加备忘录
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日历网格区域 -->
    <div class="calendar-section">
      <div class="container">
        <!-- 3×4月份网格 -->
        <div class="row g-4" id="calendarGrid">
          <!-- 月份卡片将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>

    <!-- 统计信息区域 -->
    <div class="stats-section">
      <div class="container">
        <div class="row">
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="bi bi-calendar-event"></i>
              </div>
              <div class="stat-info">
                <h4 id="totalMemos">7</h4>
                <p>总备忘录</p>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="bi bi-calendar-check"></i>
              </div>
              <div class="stat-info">
                <h4 id="todayMemos">0</h4>
                <p>今日备忘录</p>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="bi bi-calendar-heart"></i>
              </div>
              <div class="stat-info">
                <h4 id="holidays">19</h4>
                <p>节假日</p>
              </div>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="stat-card">
              <div class="stat-icon">
                <i class="bi bi-calendar-week"></i>
              </div>
              <div class="stat-info">
                <h4 id="weekends">104</h4>
                <p>周末天数</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 天气预报浮动窗口 -->
  <div class="weather-widget" id="weatherWidget">
    <div class="weather-header">
      <h6 class="mb-0">
        <i class="bi bi-cloud-sun me-2"></i>天气预报
      </h6>
      <button type="button" class="btn-close btn-close-white" id="closeWeather"></button>
    </div>
    
    <div class="weather-content">
      <!-- 天气内容将通过JavaScript填充 -->
    </div>
  </div>

  <!-- 备忘录模态框 -->
  <div class="modal fade" id="memoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="bi bi-journal-plus me-2"></i>
            <span id="memoModalTitle">添加备忘录</span>
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        
        <div class="modal-body">
          <form id="memoForm">
            <div class="mb-3">
              <label for="memoDate" class="form-label">日期</label>
              <input type="date" class="form-control" id="memoDate" required>
            </div>
            
            <div class="mb-3">
              <label for="memoTitle" class="form-label">标题</label>
              <input type="text" class="form-control" id="memoTitle" placeholder="请输入备忘录标题" required>
            </div>
            
            <div class="mb-3">
              <label for="memoContent" class="form-label">内容</label>
              <textarea class="form-control" id="memoContent" rows="4" placeholder="请输入备忘录内容（可选）"></textarea>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <label for="memoPriority" class="form-label">优先级</label>
                <select class="form-select" id="memoPriority">
                  <option value="low">低</option>
                  <option value="medium" selected>中</option>
                  <option value="high">高</option>
                </select>
              </div>
              
              <div class="col-md-6">
                <label for="memoCategory" class="form-label">分类</label>
                <select class="form-select" id="memoCategory">
                  <option value="work">工作</option>
                  <option value="personal">个人</option>
                  <option value="family">家庭</option>
                  <option value="health">健康</option>
                  <option value="other">其他</option>
                </select>
              </div>
            </div>
          </form>
        </div>
        
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="saveMemo">
            <i class="bi bi-check-circle me-1"></i>保存
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Bootstrap 5 JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- 简化的JavaScript -->
  <script>
    // 简化的日历类
    class SimpleCalendar {
      constructor() {
        this.currentYear = new Date().getFullYear();
        this.weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        this.months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
        this.memos = new Map();
        this.holidays = new Map();
        
        this.init();
      }

      init() {
        this.bindEvents();
        this.generateSampleData();
        this.updateDisplay();
      }

      bindEvents() {
        document.getElementById('prevYear').addEventListener('click', () => {
          this.changeYear(-1);
        });
        
        document.getElementById('nextYear').addEventListener('click', () => {
          this.changeYear(1);
        });
        
        document.getElementById('todayBtn').addEventListener('click', () => {
          this.goToToday();
        });

        document.getElementById('addMemoBtn').addEventListener('click', () => {
          this.openMemoModal();
        });
      }

      generateSampleData() {
        // 生成示例节假日
        const sampleHolidays = [
          { holiday_date: `${this.currentYear}-01-01`, name: '元旦', type: 'legal' },
          { holiday_date: `${this.currentYear}-02-10`, name: '春节', type: 'legal' },
          { holiday_date: `${this.currentYear}-04-05`, name: '清明节', type: 'legal' },
          { holiday_date: `${this.currentYear}-05-01`, name: '劳动节', type: 'legal' },
          { holiday_date: `${this.currentYear}-06-02`, name: '端午节', type: 'legal' },
          { holiday_date: `${this.currentYear}-10-01`, name: '国庆节', type: 'legal' },
          { holiday_date: `${this.currentYear}-02-14`, name: '情人节', type: 'traditional' },
          { holiday_date: `${this.currentYear}-03-08`, name: '妇女节', type: 'traditional' },
          { holiday_date: `${this.currentYear}-06-01`, name: '儿童节', type: 'traditional' },
          { holiday_date: `${this.currentYear}-09-10`, name: '教师节', type: 'traditional' },
          { holiday_date: `${this.currentYear}-12-25`, name: '圣诞节', type: 'traditional' }
        ];
        
        sampleHolidays.forEach(holiday => {
          const date = holiday.holiday_date;
          if (!this.holidays.has(date)) {
            this.holidays.set(date, []);
          }
          this.holidays.get(date).push(holiday);
        });

        // 生成示例备忘录
        const sampleMemos = [
          { memo_date: `${this.currentYear}-01-15`, title: '项目会议', priority: 'high', category: 'work' },
          { memo_date: `${this.currentYear}-02-14`, title: '情人节', priority: 'medium', category: 'personal' },
          { memo_date: `${this.currentYear}-03-08`, title: '妇女节', priority: 'medium', category: 'family' },
          { memo_date: `${this.currentYear}-05-01`, title: '劳动节假期', priority: 'low', category: 'personal' },
          { memo_date: `${this.currentYear}-06-01`, title: '儿童节', priority: 'high', category: 'family' },
          { memo_date: `${this.currentYear}-09-10`, title: '教师节', priority: 'medium', category: 'personal' },
          { memo_date: `${this.currentYear}-12-25`, title: '圣诞节', priority: 'medium', category: 'personal' }
        ];
        
        sampleMemos.forEach(memo => {
          const date = memo.memo_date;
          if (!this.memos.has(date)) {
            this.memos.set(date, []);
          }
          this.memos.get(date).push(memo);
        });
      }

      getChineseYear(year) {
        const heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
        const earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
        const zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
        
        const baseYear = 1984;
        const yearOffset = year - baseYear;
        const heavenlyIndex = ((yearOffset % 10) + 10) % 10;
        const earthlyIndex = ((yearOffset % 12) + 12) % 12;
        
        return heavenlyStems[heavenlyIndex] + earthlyBranches[earthlyIndex] + zodiacAnimals[earthlyIndex] + '年';
      }

      generateMonthData(month) {
        const year = this.currentYear;
        const firstDay = new Date(year, month - 1, 1);
        const lastDay = new Date(year, month, 0);
        const daysInMonth = lastDay.getDate();
        const startWeekday = firstDay.getDay();
        const today = new Date();
        
        const days = [];
        
        // 上个月的日期
        const prevMonth = month === 1 ? 12 : month - 1;
        const prevYear = month === 1 ? year - 1 : year;
        const prevMonthDays = new Date(prevYear, prevMonth, 0).getDate();
        
        for (let i = startWeekday - 1; i >= 0; i--) {
          const day = prevMonthDays - i;
          days.push({
            day: day,
            isCurrentMonth: false,
            isToday: false,
            isWeekend: false,
            date: `${prevYear}-${String(prevMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
            holidays: [],
            memoCount: 0
          });
        }
        
        // 当前月的日期
        for (let day = 1; day <= daysInMonth; day++) {
          const date = new Date(year, month - 1, day);
          const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
          const isToday = date.toDateString() === today.toDateString();
          const isWeekend = date.getDay() === 0 || date.getDay() === 6;
          const holidays = this.holidays.get(dateStr) || [];
          const memoCount = this.memos.has(dateStr) ? this.memos.get(dateStr).length : 0;
          
          days.push({
            day: day,
            isCurrentMonth: true,
            isToday: isToday,
            isWeekend: isWeekend,
            date: dateStr,
            holidays: holidays,
            memoCount: memoCount
          });
        }
        
        // 下个月的日期
        const nextMonth = month === 12 ? 1 : month + 1;
        const nextYear = month === 12 ? year + 1 : year;
        const totalCells = 42;
        const remainingCells = totalCells - days.length;
        
        for (let day = 1; day <= remainingCells && day <= 14; day++) {
          days.push({
            day: day,
            isCurrentMonth: false,
            isToday: false,
            isWeekend: false,
            date: `${nextYear}-${String(nextMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`,
            holidays: [],
            memoCount: 0
          });
        }
        
        return days;
      }

      generateMonthCard(month) {
        const monthData = this.generateMonthData(month);
        const monthName = this.months[month - 1];
        
        let html = `
          <div class="col-lg-4 col-md-6">
            <div class="month-card" onclick="alert('点击了${this.currentYear}年${monthName}')">
              <div class="month-header">
                ${this.currentYear}年${monthName}
              </div>
              
              <div class="weekdays-header">
                ${this.weekdays.map(day => `<div class="weekday-cell">${day}</div>`).join('')}
              </div>
              
              <div class="days-grid">
        `;
        
        monthData.forEach(day => {
          const classes = ['day-cell'];
          
          if (!day.isCurrentMonth) classes.push('other-month');
          if (day.isToday) classes.push('today');
          if (day.isWeekend && day.isCurrentMonth) classes.push('weekend');
          if (day.holidays.length > 0) {
            const primaryHoliday = day.holidays[0];
            classes.push(`holiday-${primaryHoliday.type}`);
          }
          if (day.memoCount > 0) classes.push('has-memo');
          
          const holidayTag = day.holidays.length > 0 ? 
            `<div class="holiday-tag">${day.holidays[0].name}</div>` : '';
          
          html += `
            <div class="${classes.join(' ')}" onclick="event.stopPropagation(); calendar.openMemoModal('${day.date}')">
              <div class="day-number">${day.day}</div>
              ${holidayTag}
              ${day.memoCount > 0 ? `<div class="memo-indicator">${day.memoCount}</div>` : ''}
            </div>
          `;
        });
        
        html += `
              </div>
            </div>
          </div>
        `;
        
        return html;
      }

      generateCalendar() {
        const grid = document.getElementById('calendarGrid');
        let html = '';
        
        for (let month = 1; month <= 12; month++) {
          html += this.generateMonthCard(month);
        }
        
        grid.innerHTML = html;
      }

      updateDisplay() {
        document.getElementById('currentYear').textContent = this.currentYear;
        document.getElementById('chineseYear').textContent = this.getChineseYear(this.currentYear);
        this.generateCalendar();
        this.updateStats();
      }

      updateStats() {
        let totalMemos = 0;
        let todayMemos = 0;
        const today = new Date().toISOString().split('T')[0];
        
        this.memos.forEach((memos, date) => {
          totalMemos += memos.length;
          if (date === today) {
            todayMemos = memos.length;
          }
        });
        
        document.getElementById('totalMemos').textContent = totalMemos;
        document.getElementById('todayMemos').textContent = todayMemos;
        
        let holidayCount = 0;
        this.holidays.forEach((holidays) => {
          holidayCount += holidays.filter(h => h.type === 'legal').length;
        });
        document.getElementById('holidays').textContent = holidayCount;
      }

      changeYear(delta) {
        this.currentYear += delta;
        this.generateSampleData();
        this.updateDisplay();
      }

      goToToday() {
        this.currentYear = new Date().getFullYear();
        this.generateSampleData();
        this.updateDisplay();
      }

      openMemoModal(date = null) {
        const modal = new bootstrap.Modal(document.getElementById('memoModal'));
        const currentDate = date || new Date().toISOString().split('T')[0];
        document.getElementById('memoDate').value = currentDate;
        modal.show();
      }
    }

    // 简化的天气管理器
    class SimpleWeatherManager {
      constructor() {
        this.widget = document.getElementById('weatherWidget');
        this.isVisible = false;
        this.init();
      }

      init() {
        this.bindEvents();
        this.createWeatherContent();
      }

      bindEvents() {
        document.getElementById('weatherBtn').addEventListener('click', () => {
          this.toggle();
        });

        document.getElementById('closeWeather').addEventListener('click', () => {
          this.hide();
        });
      }

      createWeatherContent() {
        const content = this.widget.querySelector('.weather-content');
        content.innerHTML = `
          <div class="current-weather mb-4">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">北京市</h5>
                <small class="text-muted">${new Date().toLocaleString()}</small>
              </div>
              <div class="text-end">
                <div class="h3 mb-0 text-primary">15°C</div>
                <small class="text-muted">晴</small>
              </div>
            </div>
          </div>
          
          <div class="weather-details mb-4">
            <div class="row g-3">
              <div class="col-6">
                <div class="detail-item p-2 bg-light rounded">
                  <i class="bi bi-droplet text-info"></i>
                  <span class="ms-2">湿度</span>
                  <strong class="float-end">65%</strong>
                </div>
              </div>
              <div class="col-6">
                <div class="detail-item p-2 bg-light rounded">
                  <i class="bi bi-wind text-success"></i>
                  <span class="ms-2">风速</span>
                  <strong class="float-end">8.5 km/h</strong>
                </div>
              </div>
            </div>
          </div>
          
          <div class="forecast">
            <h6 class="mb-3">未来三天</h6>
            <div class="row g-2">
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">明天</small>
                    <small class="d-block">1月17日</small>
                    <small class="d-block fw-bold">多云</small>
                    <small class="text-primary">18°/8°</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">后天</small>
                    <small class="d-block">1月18日</small>
                    <small class="d-block fw-bold">晴</small>
                    <small class="text-primary">20°/10°</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card h-100">
                  <div class="card-body p-2 text-center">
                    <small class="text-muted d-block">大后天</small>
                    <small class="d-block">1月19日</small>
                    <small class="d-block fw-bold">阴</small>
                    <small class="text-primary">16°/6°</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;
      }

      toggle() {
        if (this.isVisible) {
          this.hide();
        } else {
          this.show();
        }
      }

      show() {
        this.isVisible = true;
        this.widget.classList.add('show');
      }

      hide() {
        this.isVisible = false;
        this.widget.classList.remove('show');
      }
    }

    // 初始化应用
    let calendar, weatherManager;

    document.addEventListener('DOMContentLoaded', function() {
      console.log('页面加载完成，初始化日历...');
      calendar = new SimpleCalendar();
      weatherManager = new SimpleWeatherManager();
      
      // 备忘录保存功能
      document.getElementById('saveMemo').addEventListener('click', function() {
        const title = document.getElementById('memoTitle').value.trim();
        const date = document.getElementById('memoDate').value;
        
        if (title && date) {
          alert(`备忘录已保存：\n日期：${date}\n标题：${title}`);
          bootstrap.Modal.getInstance(document.getElementById('memoModal')).hide();
        } else {
          alert('请填写标题和日期');
        }
      });
    });
  </script>
</body>
</html>
