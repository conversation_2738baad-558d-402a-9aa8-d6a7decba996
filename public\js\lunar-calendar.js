/**
 * 农历计算库
 * 基于中国农历算法实现
 */
class LunarCalendar {
  constructor() {
    // 农历数据表 (1900-2100)
    this.lunarInfo = [
      0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
      0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
      0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
      0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
      0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557,
      0x06ca0, 0x0b550, 0x15355, 0x04da0, 0x0a5b0, 0x14573, 0x052b0, 0x0a9a8, 0x0e950, 0x06aa0,
      0x0aea6, 0x0ab50, 0x04b60, 0x0aae4, 0x0a570, 0x05260, 0x0f263, 0x0d950, 0x05b57, 0x056a0,
      0x096d0, 0x04dd5, 0x04ad0, 0x0a4d0, 0x0d4d4, 0x0d250, 0x0d558, 0x0b540, 0x0b6a0, 0x195a6,
      0x095b0, 0x049b0, 0x0a974, 0x0a4b0, 0x0b27a, 0x06a50, 0x06d40, 0x0af46, 0x0ab60, 0x09570,
      0x04af5, 0x04970, 0x064b0, 0x074a3, 0x0ea50, 0x06b58, 0x055c0, 0x0ab60, 0x096d5, 0x092e0,
      0x0c960, 0x0d954, 0x0d4a0, 0x0da50, 0x07552, 0x056a0, 0x0abb7, 0x025d0, 0x092d0, 0x0cab5,
      0x0a950, 0x0b4a0, 0x0baa4, 0x0ad50, 0x055d9, 0x04ba0, 0x0a5b0, 0x15176, 0x052b0, 0x0a930,
      0x07954, 0x06aa0, 0x0ad50, 0x05b52, 0x04b60, 0x0a6e6, 0x0a4e0, 0x0d260, 0x0ea65, 0x0d530,
      0x05aa0, 0x076a3, 0x096d0, 0x04afb, 0x04ad0, 0x0a4d0, 0x1d0b6, 0x0d250, 0x0d520, 0x0dd45,
      0x0b5a0, 0x056d0, 0x055b2, 0x049b0, 0x0a577, 0x0a4b0, 0x0aa50, 0x1b255, 0x06d20, 0x0ada0,
      0x14b63, 0x09370, 0x049f8, 0x04970, 0x064b0, 0x168a6, 0x0ea50, 0x06b20, 0x1a6c4, 0x0aae0,
      0x0a2e0, 0x0d2e3, 0x0c960, 0x0d557, 0x0d4a0, 0x0da50, 0x05d55, 0x056a0, 0x0a6d0, 0x055d4,
      0x052d0, 0x0a9b8, 0x0a950, 0x0b4a0, 0x0b6a6, 0x0ad50, 0x055a0, 0x0aba4, 0x0a5b0, 0x052b0,
      0x0b273, 0x06930, 0x07337, 0x06aa0, 0x0ad50, 0x14b55, 0x04b60, 0x0a570, 0x054e4, 0x0d160,
      0x0e968, 0x0d520, 0x0daa0, 0x16aa6, 0x056d0, 0x04ae0, 0x0a9d4, 0x0a2d0, 0x0d150, 0x0f252,
      0x0d520
    ];

    // 天干
    this.heavenlyStems = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    
    // 地支
    this.earthlyBranches = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    // 生肖
    this.zodiacAnimals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
    
    // 农历月份
    this.lunarMonths = ['正月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '冬月', '腊月'];
    
    // 农历日期
    this.lunarDays = [
      '初一', '初二', '初三', '初四', '初五', '初六', '初七', '初八', '初九', '初十',
      '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十',
      '廿一', '廿二', '廿三', '廿四', '廿五', '廿六', '廿七', '廿八', '廿九', '三十'
    ];

    // 二十四节气
    this.solarTerms = [
      '小寒', '大寒', '立春', '雨水', '惊蛰', '春分', '清明', '谷雨',
      '立夏', '小满', '芒种', '夏至', '小暑', '大暑', '立秋', '处暑',
      '白露', '秋分', '寒露', '霜降', '立冬', '小雪', '大雪', '冬至'
    ];

    // 传统节日
    this.traditionalFestivals = {
      '1-1': '春节',
      '1-15': '元宵节',
      '2-2': '龙抬头',
      '5-5': '端午节',
      '7-7': '七夕节',
      '7-15': '中元节',
      '8-15': '中秋节',
      '9-9': '重阳节',
      '12-8': '腊八节',
      '12-23': '小年',
      '12-30': '除夕'
    };

    // 法定节假日
    this.legalHolidays = {
      '1-1': '元旦',
      '2-14': '情人节',
      '3-8': '妇女节',
      '3-12': '植树节',
      '4-1': '愚人节',
      '5-1': '劳动节',
      '5-4': '青年节',
      '6-1': '儿童节',
      '7-1': '建党节',
      '8-1': '建军节',
      '9-10': '教师节',
      '10-1': '国庆节',
      '12-25': '圣诞节'
    };
  }

  /**
   * 获取农历年的天数
   */
  getLunarYearDays(year) {
    let sum = 348;
    for (let i = 0x8000; i > 0x8; i >>= 1) {
      sum += (this.lunarInfo[year - 1900] & i) ? 1 : 0;
    }
    return sum + this.getLeapDays(year);
  }

  /**
   * 获取农历年闰月的天数
   */
  getLeapDays(year) {
    if (this.getLeapMonth(year)) {
      return (this.lunarInfo[year - 1900] & 0x10000) ? 30 : 29;
    }
    return 0;
  }

  /**
   * 获取农历年闰月月份
   */
  getLeapMonth(year) {
    return this.lunarInfo[year - 1900] & 0xf;
  }

  /**
   * 获取农历月的天数
   */
  getLunarMonthDays(year, month) {
    return (this.lunarInfo[year - 1900] & (0x10000 >> month)) ? 30 : 29;
  }

  /**
   * 公历转农历
   */
  solarToLunar(year, month, day) {
    const baseDate = new Date(1900, 0, 31);
    const objDate = new Date(year, month - 1, day);
    let offset = Math.floor((objDate - baseDate) / 86400000);

    let lunarYear = 1900;
    let daysInYear = 0;
    
    while (lunarYear < 2100 && offset > 0) {
      daysInYear = this.getLunarYearDays(lunarYear);
      if (offset < daysInYear) break;
      offset -= daysInYear;
      lunarYear++;
    }

    let lunarMonth = 1;
    let daysInMonth = 0;
    let leap = this.getLeapMonth(lunarYear);
    let isLeap = false;

    while (lunarMonth < 13 && offset > 0) {
      if (leap > 0 && lunarMonth === (leap + 1) && !isLeap) {
        lunarMonth--;
        isLeap = true;
        daysInMonth = this.getLeapDays(lunarYear);
      } else {
        daysInMonth = this.getLunarMonthDays(lunarYear, lunarMonth);
      }

      if (offset < daysInMonth) break;
      
      offset -= daysInMonth;
      
      if (isLeap && lunarMonth === (leap + 1)) {
        isLeap = false;
      }
      
      lunarMonth++;
    }

    const lunarDay = offset + 1;

    return {
      year: lunarYear,
      month: lunarMonth,
      day: lunarDay,
      isLeap: isLeap,
      yearName: this.getYearName(lunarYear),
      monthName: this.getMonthName(lunarMonth, isLeap),
      dayName: this.getDayName(lunarDay)
    };
  }

  /**
   * 获取干支纪年
   */
  getYearName(year) {
    const heavenlyIndex = (year - 4) % 10;
    const earthlyIndex = (year - 4) % 12;
    const zodiacIndex = (year - 4) % 12;
    
    return this.heavenlyStems[heavenlyIndex] + 
           this.earthlyBranches[earthlyIndex] + 
           this.zodiacAnimals[zodiacIndex] + '年';
  }

  /**
   * 获取农历月份名称
   */
  getMonthName(month, isLeap = false) {
    return (isLeap ? '闰' : '') + this.lunarMonths[month - 1];
  }

  /**
   * 获取农历日期名称
   */
  getDayName(day) {
    return this.lunarDays[day - 1];
  }

  /**
   * 获取二十四节气
   */
  getSolarTerm(year, month, day) {
    // 简化的节气计算
    const solarTermDates = this.calculateSolarTerms(year);
    const dateKey = `${month}-${day}`;
    
    for (let term of solarTermDates) {
      if (term.date === dateKey) {
        return term.name;
      }
    }
    
    return null;
  }

  /**
   * 计算年度节气日期
   */
  calculateSolarTerms(year) {
    const terms = [];
    const termDates = [
      [1, 5], [1, 20], [2, 4], [2, 19], [3, 6], [3, 21],
      [4, 5], [4, 20], [5, 6], [5, 21], [6, 6], [6, 21],
      [7, 7], [7, 23], [8, 8], [8, 23], [9, 8], [9, 23],
      [10, 8], [10, 23], [11, 7], [11, 22], [12, 7], [12, 22]
    ];

    for (let i = 0; i < this.solarTerms.length; i++) {
      const [month, day] = termDates[i];
      terms.push({
        name: this.solarTerms[i],
        date: `${month}-${day}`,
        month: month,
        day: day
      });
    }

    return terms;
  }

  /**
   * 获取传统节日
   */
  getTraditionalFestival(lunarMonth, lunarDay) {
    const key = `${lunarMonth}-${lunarDay}`;
    return this.traditionalFestivals[key] || null;
  }

  /**
   * 获取法定节假日
   */
  getLegalHoliday(month, day) {
    const key = `${month}-${day}`;
    return this.legalHolidays[key] || null;
  }

  /**
   * 获取完整的日期信息
   */
  getDateInfo(year, month, day) {
    const lunar = this.solarToLunar(year, month, day);
    const solarTerm = this.getSolarTerm(year, month, day);
    const traditionalFestival = this.getTraditionalFestival(lunar.month, lunar.day);
    const legalHoliday = this.getLegalHoliday(month, day);

    return {
      solar: { year, month, day },
      lunar: lunar,
      solarTerm: solarTerm,
      traditionalFestival: traditionalFestival,
      legalHoliday: legalHoliday,
      isWeekend: new Date(year, month - 1, day).getDay() % 6 === 0
    };
  }

  /**
   * 获取当前农历日期
   */
  getCurrentLunarDate() {
    const now = new Date();
    return this.getDateInfo(now.getFullYear(), now.getMonth() + 1, now.getDate());
  }

  /**
   * 获取年度统计信息
   */
  getYearStats(year) {
    const stats = {
      totalDays: 0,
      weekends: 0,
      legalHolidays: 0,
      traditionalFestivals: 0,
      solarTerms: 24,
      lunarYear: this.getYearName(year)
    };

    // 计算全年天数
    const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    stats.totalDays = isLeapYear ? 366 : 365;

    // 计算周末天数
    for (let month = 1; month <= 12; month++) {
      const daysInMonth = new Date(year, month, 0).getDate();
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month - 1, day);
        if (date.getDay() === 0 || date.getDay() === 6) {
          stats.weekends++;
        }
        
        // 统计法定节假日
        if (this.getLegalHoliday(month, day)) {
          stats.legalHolidays++;
        }
        
        // 统计传统节日
        const lunar = this.solarToLunar(year, month, day);
        if (this.getTraditionalFestival(lunar.month, lunar.day)) {
          stats.traditionalFestivals++;
        }
      }
    }

    return stats;
  }
}

// 全局农历实例
window.lunarCalendar = new LunarCalendar();
